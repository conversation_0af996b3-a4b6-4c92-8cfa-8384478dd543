/* Some variables in the code import from the following projects
* J1 import from OSS "sentry"
* SC2, hC2 import from OSS "dotenv"
* u1 import from OSS "statsig"
* j0, I5, o9, R0 import from OSS "yoga"
* id, $l import from OSS "commander.js"
*/

// @from(Start 5933657, End 5933955)
Tk5 = async (I, d) => {
    let G = P9.map({}),
      Z = I.body,
      C = P9.take(Z, {
        message: P9.expectString
      });
    Object.assign(G, C);
    let W = new xD.InternalServerException({
      $metadata: Mr(I),
      ...G
    });
    return P9.decorateServiceException(W, I.body)
  }
// @from(Start 5933957, End 5934343)
Ok5 = async (I, d) => {
    let G = P9.map({}),
      Z = I.body,
      C = P9.take(Z, {
        message: P9.expectString,
        originalMessage: P9.expectString,
        originalStatusCode: P9.expectInt32
      });
    Object.assign(G, C);
    let W = new xD.ModelStreamErrorException({
      $metadata: Mr(I),
      ...G
    });
    return P9.decorateServiceException(W, I.body)
  }
// @from(Start 5934345, End 5934639)
mk5 = async (I, d) => {
    let G = P9.map({}),
      Z = I.body,
      C = P9.take(Z, {
        message: P9.expectString
      });
    Object.assign(G, C);
    let W = new xD.ThrottlingException({
      $metadata: Mr(I),
      ...G
    });
    return P9.decorateServiceException(W, I.body)
  }
// @from(Start 5934641, End 5934935)
lk5 = async (I, d) => {
    let G = P9.map({}),
      Z = I.body,
      C = P9.take(Z, {
        message: P9.expectString
      });
    Object.assign(G, C);
    let W = new xD.ValidationException({
      $metadata: Mr(I),
      ...G
    });
    return P9.decorateServiceException(W, I.body)
  }
// @from(Start 5934937, End 5935681)
r82 = (I, d) => {
    return d.eventStreamMarshaller.deserialize(I, async (G) => {
      if (G.chunk != null) return {
        chunk: await jk5(G.chunk, d)
      };
      if (G.internalServerException != null) return {
        internalServerException: await bk5(G.internalServerException, d)
      };
      if (G.modelStreamErrorException != null) return {
        modelStreamErrorException: await hk5(G.modelStreamErrorException, d)
      };
      if (G.validationException != null) return {
        validationException: await xk5(G.validationException, d)
      };
      if (G.throttlingException != null) return {
        throttlingException: await kk5(G.throttlingException, d)
      };
      return {
        $unknown: I
      }
    })
  }
// @from(Start 5935683, End 5935796)
bk5 = async (I, d) => {
    let G = {
      ...I,
      body: await xP(I.body, d)
    };
    return Tk5(G, d)
  }
// @from(Start 5935798, End 5935911)
hk5 = async (I, d) => {
    let G = {
      ...I,
      body: await xP(I.body, d)
    };
    return Ok5(G, d)
  }
// @from(Start 5935913, End 5936029)
jk5 = async (I, d) => {
    let G = {},
      Z = await xP(I.body, d);
    return Object.assign(G, ck5(Z, d)), G
  }
// @from(Start 5936031, End 5936144)
kk5 = async (I, d) => {
    let G = {
      ...I,
      body: await xP(I.body, d)
    };
    return mk5(G, d)
  }
// @from(Start 5936146, End 5936259)
xk5 = async (I, d) => {
    let G = {
      ...I,
      body: await xP(I.body, d)
    };
    return lk5(G, d)
  }
// @from(Start 5936261, End 5936342)
ck5 = (I, d) => {
    return P9.take(I, {
      bytes: d.base64Decoder
    })
  }
// @from(Start 5936344, End 5936611)
Mr = (I) => ({
    httpStatusCode: I.statusCode,
    requestId: I.headers["x-amzn-requestid"] ?? I.headers["x-amzn-request-id"] ?? I.headers["x-amz-request-id"] ?? "",
    extendedRequestId: I.headers["x-amz-id-2"] ?? "",
    cfId: I.headers["x-amz-cf-id"] ?? ""
  })
// @from(Start 5936613, End 5936679)
pk5 = (I, d) => P9.collectBody(I, d).then((G) => d.utf8Encoder(G))
// @from(Start 5936681, End 5936778)
xP = (I, d) => pk5(I, d).then((G) => {
    if (G.length) return JSON.parse(G);
    return {}
  })
// @from(Start 5936784, End 5936831)
LD1 = (I) => new TextDecoder("utf-8").decode(I)
// @from(Start 5936835, End 5936875)
a82 = (I) => new TextEncoder().encode(I)
// @from(Start 5936879, End 5937207)
ik5 = () => {
    let I = new s82.EventStreamMarshaller({
      utf8Encoder: LD1,
      utf8Decoder: a82
    });
    return {
      base64Decoder: Sr.fromBase64,
      base64Encoder: Sr.toBase64,
      utf8Decoder: a82,
      utf8Encoder: LD1,
      eventStreamMarshaller: I,
      streamCollector: o82.streamCollector
    }
  }
// @from(Start 5937209, End 5939013)
class Lr extends BI {
  static fromSSEResponse(I, d) {
    let G = !1;
    async function* Z() {
      if (!I.body) throw d.abort(), new Y4("Attempted to iterate over a response with no body");
      let W = Og(I.body),
        w = r82(W, ik5());
      for await (let B of w) if (B.chunk && B.chunk.bytes) yield {
        event: "chunk",
        data: LD1(B.chunk.bytes),
        raw: []
      };
      else if (B.internalServerException) yield {
        event: "error",
        data: "InternalServerException",
        raw: []
      };
      else if (B.modelStreamErrorException) yield {
        event: "error",
        data: "ModelStreamErrorException",
        raw: []
      };
      else if (B.validationException) yield {
        event: "error",
        data: "ValidationException",
        raw: []
      };
      else if (B.throttlingException) yield {
        event: "error",
        data: "ThrottlingException",
        raw: []
      }
    }
    async function* C() {
      if (G) throw new Error("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");
      G = !0;
      let W = !1;
      try {
        for await (let w of Z()) {
          if (w.event === "chunk") try {
            yield JSON.parse(w.data)
          } catch (B) {
            throw console.error("Could not parse message into JSON:", w.data), console.error("From chunk:", w.raw), B
          }
          if (w.event === "error") {
            let B = w.data,
              A = C71(B),
              V = A ? void 0 : B;
            throw f9.generate(void 0, A, V, jS(I.headers))
          }
        }
        W = !0
      } catch (w) {
        if (w instanceof Error && w.name === "AbortError") return;
        throw w
      } finally {
        if (!W) d.abort()
      }
    }
    return new Lr(C, d)
  }
}
// @from(Start 5939018, End 5939044)
nk5 = "bedrock-2023-05-31"
// @from(Start 5939048, End 5939121)
rk5 = new Set(["/v1/complete", "/v1/messages", "/v1/messages?beta=true"])
// @from(Start 5939123, End 5941361)
class yr extends mg {
  constructor({
    baseURL: I = lC("ANTHROPIC_BEDROCK_BASE_URL"),
    awsSecretKey: d = null,
    awsAccessKey: G = null,
    awsRegion: Z = lC("AWS_REGION") ?? "us-east-1",
    awsSessionToken: C = null,
    ...W
  } = {}) {
    let w = {
      awsSecretKey: d,
      awsAccessKey: G,
      awsRegion: Z,
      awsSessionToken: C,
      ...W,
      baseURL: I || `https://bedrock-runtime.${Z}.amazonaws.com`
    };
    super({
      baseURL: w.baseURL,
      timeout: w.timeout ?? 600000,
      httpAgent: w.httpAgent,
      maxRetries: w.maxRetries,
      fetch: w.fetch
    });
    this.messages = ak5(this), this.completions = new U_(this), this.beta = sk5(this), this._options = w, this.awsSecretKey = d, this.awsAccessKey = G, this.awsRegion = Z, this.awsSessionToken = C
  }
  defaultQuery() {
    return this._options.defaultQuery
  }
  defaultHeaders(I) {
    return {
      ...super.defaultHeaders(I),
      ...this._options.defaultHeaders
    }
  }
  async prepareRequest(I, {
    url: d,
    options: G
  }) {
    let Z = this.awsRegion;
    if (!Z) throw new Error("Expected `awsRegion` option to be passed to the client or the `AWS_REGION` environment variable to be present");
    let C = await tk0(I, {
      url: d,
      regionName: Z,
      awsAccessKey: this.awsAccessKey,
      awsSecretKey: this.awsSecretKey,
      awsSessionToken: this.awsSessionToken
    });
    I.headers = {
      ...I.headers,
      ...C
    }
  }
  buildRequest(I) {
    if (I.__streamClass = Lr, q_(I.body)) I.body = {
      ...I.body
    };
    if (q_(I.body)) {
      if (!I.body.anthropic_version) I.body.anthropic_version = nk5;
      if (I.headers && !I.body.anthropic_beta) {
        let d = gf(I.headers, "anthropic-beta");
        if (d != null) I.body.anthropic_beta = d.split(",")
      }
    }
    if (rk5.has(I.path) && I.method === "post") {
      if (!q_(I.body)) throw new Error("Expected request body to be an object for post /v1/messages");
      let d = I.body.model;
      I.body.model = void 0;
      let G = I.body.stream;
      if (I.body.stream = void 0, G) I.path = `/model/${d}/invoke-with-response-stream`;
      else I.path = `/model/${d}/invoke`
    }
    return super.buildRequest(I)
  }
}
// @from(Start 5941363, End 5941454)
function ak5(I) {
  let d = new NZ(I);
  return delete d.batches, delete d.countTokens, d
}
// @from(Start 5941456, End 5941589)
function sk5(I) {
  let d = new IG(I);
  return delete d.promptCaching, delete d.messages.batches, delete d.messages.countTokens, d
}
// @from(Start 5941594, End 5941612)
LC2 = J1(SC2(), 1)
// @from(Start 5941616, End 5941641)
Ea5 = "vertex-2023-10-16"
// @from(Start 5941645, End 5941702)
Ma5 = new Set(["/v1/messages", "/v1/messages?beta=true"])
// @from(Start 5941704, End 5944490)
class Xa extends mg {
  constructor({
    baseURL: I = lC("ANTHROPIC_VERTEX_BASE_URL"),
    region: d = lC("CLOUD_ML_REGION") ?? null,
    projectId: G = lC("ANTHROPIC_VERTEX_PROJECT_ID") ?? null,
    ...Z
  } = {}) {
    if (!d) throw new Error("No region was given. The client should be instantiated with the `region` option or the `CLOUD_ML_REGION` environment variable should be set.");
    let C = {
      ...Z,
      baseURL: I || `https://${d}-aiplatform.googleapis.com/v1`
    };
    super({
      baseURL: C.baseURL,
      timeout: C.timeout ?? 600000,
      httpAgent: C.httpAgent,
      maxRetries: C.maxRetries,
      fetch: C.fetch
    });
    this.messages = Sa5(this), this.beta = La5(this), this._options = C, this.region = d, this.projectId = G, this.accessToken = C.accessToken ?? null, this._auth = C.googleAuth ?? new LC2.GoogleAuth({
      scopes: "https://www.googleapis.com/auth/cloud-platform"
    }), this._authClientPromise = this._auth.getClient()
  }
  defaultQuery() {
    return this._options.defaultQuery
  }
  defaultHeaders(I) {
    return {
      ...super.defaultHeaders(I),
      ...this._options.defaultHeaders
    }
  }
  async prepareOptions(I) {
    let d = await this._authClientPromise,
      G = await d.getRequestHeaders(),
      Z = d.projectId ?? G["x-goog-user-project"];
    if (!this.projectId && Z) this.projectId = Z;
    I.headers = {
      ...G,
      ...I.headers
    }
  }
  buildRequest(I) {
    if (q_(I.body)) I.body = {
      ...I.body
    };
    if (q_(I.body)) {
      if (!I.body.anthropic_version) I.body.anthropic_version = Ea5
    }
    if (Ma5.has(I.path) && I.method === "post") {
      if (!this.projectId) throw new Error("No projectId was given and it could not be resolved from credentials. The client should be instantiated with the `projectId` option or the `ANTHROPIC_VERTEX_PROJECT_ID` environment variable should be set.");
      if (!q_(I.body)) throw new Error("Expected request body to be an object for post /v1/messages");
      let d = I.body.model;
      I.body.model = void 0;
      let Z = I.body.stream ?? !1 ? "streamRawPredict" : "rawPredict";
      I.path = `/projects/${this.projectId}/locations/${this.region}/publishers/anthropic/models/${d}:${Z}`
    }
    if (I.path === "/v1/messages/count_tokens" || I.path == "/v1/messages/count_tokens?beta=true" && I.method === "post") {
      if (!this.projectId) throw new Error("No projectId was given and it could not be resolved from credentials. The client should be instantiated with the `projectId` option or the `ANTHROPIC_VERTEX_PROJECT_ID` environment variable should be set.");
      I.path = `/projects/${this.projectId}/locations/${this.region}/publishers/anthropic/models/count-tokens:rawPredict`
    }
    return super.buildRequest(I)
  }
}
// @from(Start 5944492, End 5944561)
function Sa5(I) {
  let d = new NZ(I);
  return delete d.batches, d
}
// @from(Start 5944563, End 5944696)
function La5(I) {
  let d = new IG(I);
  return delete d.promptCaching, delete d.messages.batches, delete d.messages.countTokens, d
}
// @from(Start 5944701, End 5944719)
Uz3 = J1(hC2(), 1)
// @from(Start 5944791, End 5944808)
xC2 = J1(u1(), 1)
// @from(Start 5944811, End 5944971)
function jC2(I, d) {
  let G = [],
    Z = "";
  for (let C of I)
    if ([...Z].length < d) Z += C;
    else G.push(Z), Z = C;
  if (Z) G.push(Z);
  return G
}
// @from(Start 5944973, End 5945250)
function X$(I) {
  if (I < 60000) return `${(I/1000).toFixed(1)}s`;
  let d = Math.floor(I / 3600000),
    G = Math.floor(I % 3600000 / 60000),
    Z = (I % 60000 / 1000).toFixed(1);
  if (d > 0) return `${d}h ${G}m ${Z}s`;
  if (G > 0) return `${G}m ${Z}s`;
  return `${Z}s`
}
// @from(Start 5945252, End 5945393)
function kC2(I) {
  return new Intl.NumberFormat("en", {
    notation: "compact",
    maximumFractionDigits: 1
  }).format(I).toLowerCase()
}
// @from(Start 5945398, End 5945469)
sD = {
  totalCost: 0,
  totalAPIDuration: 0,
  startTime: Date.now()
}
// @from(Start 5945472, End 5945539)
function _a(I, d) {
  sD.totalCost += I, sD.totalAPIDuration += d
}
// @from(Start 5945541, End 5945580)
function Da() {
  return sD.totalCost
}
// @from(Start 5945582, End 5945635)
function cC2() {
  return Date.now() - sD.startTime
}
// @from(Start 5945637, End 5945712)
function Is5(I) {
  return `$${I>0.5?ds5(I,100).toFixed(2):I.toFixed(4)}`
}
// @from(Start 5945714, End 5945870)
function FF1() {
  return j0.grey(`Total cost: ${Is5(sD.totalCost)}
Total duration (API): ${X$(sD.totalAPIDuration)}
Total duration (wall): ${X$(cC2())}`)
}
// @from(Start 5945872, End 5946261)
function pC2() {
  xC2.useEffect(() => {
    let I = () => {
      process.stdout.write(`
` + FF1() + `
`);
      let d = I5();
      o9({
        ...d,
        lastCost: sD.totalCost,
        lastAPIDuration: sD.totalAPIDuration,
        lastDuration: cC2(),
        lastSessionId: id
      })
    };
    return process.on("exit", I), () => {
      process.off("exit", I)
    }
  }, [])
}
// @from(Start 5946263, End 5946316)
function ds5(I, d) {
  return Math.round(I * d) / d
}
// @from(Start 5946318, End 5946737)
function oD(I) {
  let d = I.length - 1;
  while (d >= 0) {
    let G = I[d];
    if (G?.type === "assistant" && "usage" in G.message && !(G.message.content[0]?.type === "text" && nC2.has(G.message.content[0].text))) {
      let {
        usage: Z
      } = G.message;
      return Z.input_tokens + (Z.cache_creation_input_tokens ?? 0) + (Z.cache_read_input_tokens ?? 0) + Z.output_tokens
    }
    d--
  }
  return 0
}
// @from(Start 5946739, End 5947042)
function iC2(I) {
  let d = I.length - 1;
  while (d >= 0) {
    let G = I[d];
    if (G?.type === "assistant" && "usage" in G.message) {
      let {
        usage: Z
      } = G.message;
      return (Z.cache_creation_input_tokens ?? 0) + (Z.cache_read_input_tokens ?? 0)
    }
    d--
  }
  return 0
}
// @from(Start 5947289, End 5947336)
async function gF1(I, d) {
  return await d()
}
// @from(Start 5947338, End 5948253)
function ws5(I, d) {
  return I.map((G) => {
    if (typeof G === "string") return d(G);
    return G.map((Z) => {
      switch (Z.type) {
        case "tool_result":
          if (typeof Z.content === "string") return {
            ...Z,
            content: d(Z.content)
          };
          if (Array.isArray(Z.content)) return {
            ...Z,
            content: Z.content.map((C) => {
              switch (C.type) {
                case "text":
                  return {
                    ...C, text: d(C.text)
                  };
                case "image":
                  return C
              }
            })
          };
          return Z;
        case "text":
          return {
            ...Z, text: d(Z.text)
          };
        case "tool_use":
          return {
            ...Z, input: $l(Z.input, d)
          };
        case "image":
          return Z
      }
    })
  })
}
// @from(Start 5948255, End 5948800)
function sC2(I, d) {
  return {
    durationMs: "DURATION",
    costUSD: "COST",
    uuid: "UUID",
    message: {
      ...I.message,
      content: I.message.content.map((G) => {
        switch (G.type) {
          case "text":
            return {
              ...G, text: d(G.text), citations: G.citations || []
            };
          case "tool_use":
            return {
              ...G, input: $l(G.input, d)
            };
          default:
            return G
        }
      }).filter(Boolean)
    },
    type: "assistant"
  }
}
// @from(Start 5948802, End 5949176)
function oC2(I) {
  if (typeof I !== "string") return I;
  let d = I.replace(/num_files="\d+"/g, 'num_files="[NUM]"').replace(/duration_ms="\d+"/g, 'duration_ms="[DURATION]"').replace(/cost_usd="\d+"/g, 'cost_usd="[COST]"').replace(/\//g, eC2.sep).replaceAll(R0(), "[CWD]");
  if (d.includes("Files modified by user:")) return "Files modified by user: [FILES]";
  return d
}
// @from(Start 5949178, End 5949330)
function Bs5(I) {
  if (typeof I !== "string") return I;
  return I.replaceAll("[NUM]", "1").replaceAll("[DURATION]", "100").replaceAll("[CWD]", R0())
}
// @from(Start 5949335, End 5949400)
IW2 = Symbol("Let zodToJsonSchema decide on which parser to use")
// @from(Start 5949404, End 5949963)
tC2 = {
    name: void 0,
    $refStrategy: "root",
    basePath: ["#"],
    effectStrategy: "input",
    pipeStrategy: "all",
    dateStrategy: "format:date-time",
    mapStrategy: "entries",
    removeAdditionalStrategy: "passthrough",
    definitionPath: "definitions",
    target: "jsonSchema7",
    strictUnions: !1,
    definitions: {},
    errorMessages: !1,
    markdownDescription: !1,
    patternStrategy: "escape",
    applyRegexFlags: !1,
    emailStrategy: "format:email",
    base64Strategy: "contentEncoding:base64",
    nameStrategy: "ref"
  }
// @from(Start 5949967, End 5950062)
dW2 = (I) => typeof I === "string" ? {
    ...tC2,
    name: I
  } : {
    ...tC2,
    ...I
  }
// @from(Start 5950068, End 5950435)
GW2 = (I) => {
  let d = dW2(I),
    G = d.name !== void 0 ? [...d.basePath, d.definitionPath, d.name] : d.basePath;
  return {
    ...d,
    currentPath: G,
    propertyPath: void 0,
    seen: new Map(Object.entries(d.definitions).map(([Z, C]) => [C._def, {
      def: C._def,
      path: [...d.basePath, d.definitionPath, Z],
      jsonSchema: void 0
    }]))
  }
}
// @from(Start 5950438, End 5950565)
function JF1(I, d, G, Z) {
  if (!Z?.errorMessages) return;
  if (G) I.errorMessage = {
    ...I.errorMessage,
    [d]: G
  }
}
// @from(Start 5950567, End 5950625)
function X5(I, d, G, Z, C) {
  I[d] = G, JF1(I, d, Z, C)
}
// @from(Start 5950630, End 5950632)
N5
// @from(Start 5951852, End 5951855)
NF1
// @from(Start 5951975, End 5952187)
_2 = N5.arrayToEnum(["string", "nan", "number", "integer", "float", "boolean", "date", "bigint", "symbol", "function", "undefined", "null", "array", "object", "unknown", "promise", "void", "never", "map", "set"])
// @from(Start 5952191, End 5953117)
dX = (I) => {
    switch (typeof I) {
      case "undefined":
        return _2.undefined;
      case "string":
        return _2.string;
      case "number":
        return isNaN(I) ? _2.nan : _2.number;
      case "boolean":
        return _2.boolean;
      case "function":
        return _2.function;
      case "bigint":
        return _2.bigint;
      case "symbol":
        return _2.symbol;
      case "object":
        if (Array.isArray(I)) return _2.array;
        if (I === null) return _2.null;
        if (I.then && typeof I.then === "function" && I.catch && typeof I.catch === "function") return _2.promise;
        if (typeof Map !== "undefined" && I instanceof Map) return _2.map;
        if (typeof Set !== "undefined" && I instanceof Set) return _2.set;
        if (typeof Date !== "undefined" && I instanceof Date) return _2.date;
        return _2.object;
      default:
        return _2.unknown
    }
  }
// @from(Start 5953121, End 5953443)
y0 = N5.arrayToEnum(["invalid_type", "invalid_literal", "custom", "invalid_union", "invalid_union_discriminator", "invalid_enum_value", "unrecognized_keys", "invalid_arguments", "invalid_return_type", "invalid_date", "invalid_string", "too_small", "too_big", "invalid_intersection_types", "not_multiple_of", "not_finite"])
// @from(Start 5953447, End 5953533)
As5 = (I) => {
    return JSON.stringify(I, null, 2).replace(/"([^"]+)":/g, "$1:")
  }
// @from(Start 5953535, End 5955441)
class AG extends Error {
  get errors() {
    return this.issues
  }
  constructor(I) {
    super();
    this.issues = [], this.addIssue = (G) => {
      this.issues = [...this.issues, G]
    }, this.addIssues = (G = []) => {
      this.issues = [...this.issues, ...G]
    };
    let d = new.target.prototype;
    if (Object.setPrototypeOf) Object.setPrototypeOf(this, d);
    else this.__proto__ = d;
    this.name = "ZodError", this.issues = I
  }
  format(I) {
    let d = I || function(C) {
        return C.message
      },
      G = {
        _errors: []
      },
      Z = (C) => {
        for (let W of C.issues)
          if (W.code === "invalid_union") W.unionErrors.map(Z);
          else if (W.code === "invalid_return_type") Z(W.returnTypeError);
        else if (W.code === "invalid_arguments") Z(W.argumentsError);
        else if (W.path.length === 0) G._errors.push(d(W));
        else {
          let w = G,
            B = 0;
          while (B < W.path.length) {
            let A = W.path[B];
            if (B !== W.path.length - 1) w[A] = w[A] || {
              _errors: []
            };
            else w[A] = w[A] || {
              _errors: []
            }, w[A]._errors.push(d(W));
            w = w[A], B++
          }
        }
      };
    return Z(this), G
  }
  static assert(I) {
    if (!(I instanceof AG)) throw new Error(`Not a ZodError: ${I}`)
  }
  toString() {
    return this.message
  }
  get message() {
    return JSON.stringify(this.issues, N5.jsonStringifyReplacer, 2)
  }
  get isEmpty() {
    return this.issues.length === 0
  }
  flatten(I = (d) => d.message) {
    let d = {},
      G = [];
    for (let Z of this.issues)
      if (Z.path.length > 0) d[Z.path[0]] = d[Z.path[0]] || [], d[Z.path[0]].push(I(Z));
      else G.push(I(Z));
    return {
      formErrors: G,
      fieldErrors: d
    }
  }
  get formErrors() {
    return this.flatten()
  }
}
// @from(Start 5955489, End 5959257)
wR = (I, d) => {
    let G;
    switch (I.code) {
      case y0.invalid_type:
        if (I.received === _2.undefined) G = "Required";
        else G = `Expected ${I.expected}, received ${I.received}`;
        break;
      case y0.invalid_literal:
        G = `Invalid literal value, expected ${JSON.stringify(I.expected,N5.jsonStringifyReplacer)}`;
        break;
      case y0.unrecognized_keys:
        G = `Unrecognized key(s) in object: ${N5.joinValues(I.keys,", ")}`;
        break;
      case y0.invalid_union:
        G = "Invalid input";
        break;
      case y0.invalid_union_discriminator:
        G = `Invalid discriminator value. Expected ${N5.joinValues(I.options)}`;
        break;
      case y0.invalid_enum_value:
        G = `Invalid enum value. Expected ${N5.joinValues(I.options)}, received '${I.received}'`;
        break;
      case y0.invalid_arguments:
        G = "Invalid function arguments";
        break;
      case y0.invalid_return_type:
        G = "Invalid function return type";
        break;
      case y0.invalid_date:
        G = "Invalid date";
        break;
      case y0.invalid_string:
        if (typeof I.validation === "object")
          if ("includes" in I.validation) {
            if (G = `Invalid input: must include "${I.validation.includes}"`, typeof I.validation.position === "number") G = `${G} at one or more positions greater than or equal to ${I.validation.position}`
          } else if ("startsWith" in I.validation) G = `Invalid input: must start with "${I.validation.startsWith}"`;
        else if ("endsWith" in I.validation) G = `Invalid input: must end with "${I.validation.endsWith}"`;
        else N5.assertNever(I.validation);
        else if (I.validation !== "regex") G = `Invalid ${I.validation}`;
        else G = "Invalid";
        break;
      case y0.too_small:
        if (I.type === "array") G = `Array must contain ${I.exact?"exactly":I.inclusive?"at least":"more than"} ${I.minimum} element(s)`;
        else if (I.type === "string") G = `String must contain ${I.exact?"exactly":I.inclusive?"at least":"over"} ${I.minimum} character(s)`;
        else if (I.type === "number") G = `Number must be ${I.exact?"exactly equal to ":I.inclusive?"greater than or equal to ":"greater than "}${I.minimum}`;
        else if (I.type === "date") G = `Date must be ${I.exact?"exactly equal to ":I.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(I.minimum))}`;
        else G = "Invalid input";
        break;
      case y0.too_big:
        if (I.type === "array") G = `Array must contain ${I.exact?"exactly":I.inclusive?"at most":"less than"} ${I.maximum} element(s)`;
        else if (I.type === "string") G = `String must contain ${I.exact?"exactly":I.inclusive?"at most":"under"} ${I.maximum} character(s)`;
        else if (I.type === "number") G = `Number must be ${I.exact?"exactly":I.inclusive?"less than or equal to":"less than"} ${I.maximum}`;
        else if (I.type === "bigint") G = `BigInt must be ${I.exact?"exactly":I.inclusive?"less than or equal to":"less than"} ${I.maximum}`;
        else if (I.type === "date") G = `Date must be ${I.exact?"exactly":I.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(I.maximum))}`;
        else G = "Invalid input";
        break;
      case y0.custom:
        G = "Invalid input";
        break;
      case y0.invalid_intersection_types:
        G = "Intersection results could not be merged";
        break;
      case y0.not_multiple_of:
        G = `Number must be a multiple of ${I.multipleOf}`;
        break;
      case y0.not_finite:
        G = "Number must be finite";
        break;
      default:
        G = d.defaultError, N5.assertNever(I)
    }
    return {
      message: G
    }
  }
// @from(Start 5959261, End 5959269)
WW2 = wR
// @from(Start 5959272, End 5959301)
function Vs5(I) {
  WW2 = I
}
// @from(Start 5959303, End 5959333)
function Ha() {
  return WW2
}
// @from(Start 5959338, End 5959825)
Fa = (I) => {
    let {
      data: d,
      path: G,
      errorMaps: Z,
      issueData: C
    } = I, W = [...G, ...C.path || []], w = {
      ...C,
      path: W
    };
    if (C.message !== void 0) return {
      ...C,
      path: W,
      message: C.message
    };
    let B = "",
      A = Z.filter((V) => !!V).slice().reverse();
    for (let V of A) B = V(w, {
      data: d,
      defaultError: B
    }).message;
    return {
      ...C,
      path: W,
      message: B
    }
  }
// @from(Start 5959829, End 5959837)
Xs5 = []
// @from(Start 5959840, End 5960095)
function d2(I, d) {
  let G = Ha(),
    Z = Fa({
      issueData: d,
      data: I.data,
      path: I.path,
      errorMaps: [I.common.contextualErrorMap, I.schemaErrorMap, G, G === wR ? void 0 : wR].filter((C) => !!C)
    });
  I.common.issues.push(Z)
}
// @from(Start 5960096, End 5961257)
class h7 {
  constructor() {
    this.value = "valid"
  }
  dirty() {
    if (this.value === "valid") this.value = "dirty"
  }
  abort() {
    if (this.value !== "aborted") this.value = "aborted"
  }
  static mergeArray(I, d) {
    let G = [];
    for (let Z of d) {
      if (Z.status === "aborted") return w4;
      if (Z.status === "dirty") I.dirty();
      G.push(Z.value)
    }
    return {
      status: I.value,
      value: G
    }
  }
  static async mergeObjectAsync(I, d) {
    let G = [];
    for (let Z of d) {
      let C = await Z.key,
        W = await Z.value;
      G.push({
        key: C,
        value: W
      })
    }
    return h7.mergeObjectSync(I, G)
  }
  static mergeObjectSync(I, d) {
    let G = {};
    for (let Z of d) {
      let {
        key: C,
        value: W
      } = Z;
      if (C.status === "aborted") return w4;
      if (W.status === "aborted") return w4;
      if (C.status === "dirty") I.dirty();
      if (W.status === "dirty") I.dirty();
      if (C.value !== "__proto__" && (typeof W.value !== "undefined" || Z.alwaysSet)) G[C.value] = W.value
    }
    return {
      status: I.value,
      value: G
    }
  }
}
// @from(Start 5961262, End 5961309)
w4 = Object.freeze({
    status: "aborted"
  })
// @from(Start 5961313, End 5961366)
CR = (I) => ({
    status: "dirty",
    value: I
  })
// @from(Start 5961370, End 5961423)
DI = (I) => ({
    status: "valid",
    value: I
  })
// @from(Start 5961427, End 5961462)
zF1 = (I) => I.status === "aborted"
// @from(Start 5961466, End 5961499)
QF1 = (I) => I.status === "dirty"
// @from(Start 5961503, End 5961535)
mJ = (I) => I.status === "valid"
// @from(Start 5961539, End 5961605)
D$ = (I) => typeof Promise !== "undefined" && I instanceof Promise
// @from(Start 5961608, End 5961956)
function ga(I, d, G, Z) {
  if (G === "a" && !Z) throw new TypeError("Private accessor was defined without a getter");
  if (typeof d === "function" ? I !== d || !Z : !d.has(I)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
  return G === "m" ? Z : G === "a" ? Z.call(I) : Z ? Z.value : d.get(I)
}
// @from(Start 5961958, End 5962378)
function wW2(I, d, G, Z, C) {
  if (Z === "m") throw new TypeError("Private method is not writable");
  if (Z === "a" && !C) throw new TypeError("Private accessor was defined without a setter");
  if (typeof d === "function" ? I !== d || !C : !d.has(I)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
  return Z === "a" ? C.call(I, G) : C ? C.value = G : d.set(I, G), G
}
// @from(Start 5962383, End 5962385)
M2
// @from(Start 5962601, End 5962603)
Y$
// @from(Start 5962605, End 5962607)
_$
// @from(Start 5962609, End 5962973)
class XW {
  constructor(I, d, G, Z) {
    this._cachedPath = [], this.parent = I, this.data = d, this._path = G, this._key = Z
  }
  get path() {
    if (!this._cachedPath.length)
      if (this._key instanceof Array) this._cachedPath.push(...this._path, ...this._key);
      else this._cachedPath.push(...this._path, this._key);
    return this._cachedPath
  }
}
// @from(Start 5962978, End 5963363)
ZW2 = (I, d) => {
  if (mJ(d)) return {
    success: !0,
    data: d.value
  };
  else {
    if (!I.common.issues.length) throw new Error("Validation failed but no issues detected.");
    return {
      success: !1,
      get error() {
        if (this._error) return this._error;
        let G = new AG(I.common.issues);
        return this._error = G, this._error
      }
    }
  }
}
// @from(Start 5963366, End 5964327)
function S4(I) {
  if (!I) return {};
  let {
    errorMap: d,
    invalid_type_error: G,
    required_error: Z,
    description: C
  } = I;
  if (d && (G || Z)) throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);
  if (d) return {
    errorMap: d,
    description: C
  };
  return {
    errorMap: (w, B) => {
      var A, V;
      let {
        message: X
      } = I;
      if (w.code === "invalid_enum_value") return {
        message: X !== null && X !== void 0 ? X : B.defaultError
      };
      if (typeof B.data === "undefined") return {
        message: (A = X !== null && X !== void 0 ? X : Z) !== null && A !== void 0 ? A : B.defaultError
      };
      if (w.code !== "invalid_type") return {
        message: B.defaultError
      };
      return {
        message: (V = X !== null && X !== void 0 ? X : G) !== null && V !== void 0 ? V : B.defaultError
      }
    },
    description: C
  }
}
// @from(Start 5964328, End 5971025)
class P4 {
  get description() {
    return this._def.description
  }
  _getType(I) {
    return dX(I.data)
  }
  _getOrReturnCtx(I, d) {
    return d || {
      common: I.parent.common,
      data: I.data,
      parsedType: dX(I.data),
      schemaErrorMap: this._def.errorMap,
      path: I.path,
      parent: I.parent
    }
  }
  _processInputParams(I) {
    return {
      status: new h7,
      ctx: {
        common: I.parent.common,
        data: I.data,
        parsedType: dX(I.data),
        schemaErrorMap: this._def.errorMap,
        path: I.path,
        parent: I.parent
      }
    }
  }
  _parseSync(I) {
    let d = this._parse(I);
    if (D$(d)) throw new Error("Synchronous parse encountered promise.");
    return d
  }
  _parseAsync(I) {
    let d = this._parse(I);
    return Promise.resolve(d)
  }
  parse(I, d) {
    let G = this.safeParse(I, d);
    if (G.success) return G.data;
    throw G.error
  }
  safeParse(I, d) {
    var G;
    let Z = {
        common: {
          issues: [],
          async: (G = d === null || d === void 0 ? void 0 : d.async) !== null && G !== void 0 ? G : !1,
          contextualErrorMap: d === null || d === void 0 ? void 0 : d.errorMap
        },
        path: (d === null || d === void 0 ? void 0 : d.path) || [],
        schemaErrorMap: this._def.errorMap,
        parent: null,
        data: I,
        parsedType: dX(I)
      },
      C = this._parseSync({
        data: I,
        path: Z.path,
        parent: Z
      });
    return ZW2(Z, C)
  }
  "~validate"(I) {
    var d, G;
    let Z = {
      common: {
        issues: [],
        async: !!this["~standard"].async
      },
      path: [],
      schemaErrorMap: this._def.errorMap,
      parent: null,
      data: I,
      parsedType: dX(I)
    };
    if (!this["~standard"].async) try {
      let C = this._parseSync({
        data: I,
        path: [],
        parent: Z
      });
      return mJ(C) ? {
        value: C.value
      } : {
        issues: Z.common.issues
      }
    } catch (C) {
      if ((G = (d = C === null || C === void 0 ? void 0 : C.message) === null || d === void 0 ? void 0 : d.toLowerCase()) === null || G === void 0 ? void 0 : G.includes("encountered")) this["~standard"].async = !0;
      Z.common = {
        issues: [],
        async: !0
      }
    }
    return this._parseAsync({
      data: I,
      path: [],
      parent: Z
    }).then((C) => mJ(C) ? {
      value: C.value
    } : {
      issues: Z.common.issues
    })
  }
  async parseAsync(I, d) {
    let G = await this.safeParseAsync(I, d);
    if (G.success) return G.data;
    throw G.error
  }
  async safeParseAsync(I, d) {
    let G = {
        common: {
          issues: [],
          contextualErrorMap: d === null || d === void 0 ? void 0 : d.errorMap,
          async: !0
        },
        path: (d === null || d === void 0 ? void 0 : d.path) || [],
        schemaErrorMap: this._def.errorMap,
        parent: null,
        data: I,
        parsedType: dX(I)
      },
      Z = this._parse({
        data: I,
        path: G.path,
        parent: G
      }),
      C = await (D$(Z) ? Z : Promise.resolve(Z));
    return ZW2(G, C)
  }
  refine(I, d) {
    let G = (Z) => {
      if (typeof d === "string" || typeof d === "undefined") return {
        message: d
      };
      else if (typeof d === "function") return d(Z);
      else return d
    };
    return this._refinement((Z, C) => {
      let W = I(Z),
        w = () => C.addIssue({
          code: y0.custom,
          ...G(Z)
        });
      if (typeof Promise !== "undefined" && W instanceof Promise) return W.then((B) => {
        if (!B) return w(), !1;
        else return !0
      });
      if (!W) return w(), !1;
      else return !0
    })
  }
  refinement(I, d) {
    return this._refinement((G, Z) => {
      if (!I(G)) return Z.addIssue(typeof d === "function" ? d(G, Z) : d), !1;
      else return !0
    })
  }
  _refinement(I) {
    return new bZ({
      schema: this,
      typeName: T0.ZodEffects,
      effect: {
        type: "refinement",
        refinement: I
      }
    })
  }
  superRefine(I) {
    return this._refinement(I)
  }
  constructor(I) {
    this.spa = this.safeParseAsync, this._def = I, this.parse = this.parse.bind(this), this.safeParse = this.safeParse.bind(this), this.parseAsync = this.parseAsync.bind(this), this.safeParseAsync = this.safeParseAsync.bind(this), this.spa = this.spa.bind(this), this.refine = this.refine.bind(this), this.refinement = this.refinement.bind(this), this.superRefine = this.superRefine.bind(this), this.optional = this.optional.bind(this), this.nullable = this.nullable.bind(this), this.nullish = this.nullish.bind(this), this.array = this.array.bind(this), this.promise = this.promise.bind(this), this.or = this.or.bind(this), this.and = this.and.bind(this), this.transform = this.transform.bind(this), this.brand = this.brand.bind(this), this.default = this.default.bind(this), this.catch = this.catch.bind(this), this.describe = this.describe.bind(this), this.pipe = this.pipe.bind(this), this.readonly = this.readonly.bind(this), this.isNullable = this.isNullable.bind(this), this.isOptional = this.isOptional.bind(this), this["~standard"] = {
      version: 1,
      vendor: "zod",
      validate: (d) => this["~validate"](d)
    }
  }
  optional() {
    return VG.create(this, this._def)
  }
  nullable() {
    return GX.create(this, this._def)
  }
  nullish() {
    return this.nullable().optional()
  }
  array() {
    return VW.create(this)
  }
  promise() {
    return jJ.create(this, this._def)
  }
  or(I) {
    return XR.create([this, I], this._def)
  }
  and(I) {
    return YR.create(this, I, this._def)
  }
  transform(I) {
    return new bZ({
      ...S4(this._def),
      schema: this,
      typeName: T0.ZodEffects,
      effect: {
        type: "transform",
        transform: I
      }
    })
  }
  default (I) {
    let d = typeof I === "function" ? I : () => I;
    return new FR({
      ...S4(this._def),
      innerType: this,
      defaultValue: d,
      typeName: T0.ZodDefault
    })
  }
  brand() {
    return new Ka({
      typeName: T0.ZodBranded,
      type: this,
      ...S4(this._def)
    })
  } catch (I) {
    let d = typeof I === "function" ? I : () => I;
    return new gR({
      ...S4(this._def),
      innerType: this,
      catchValue: d,
      typeName: T0.ZodCatch
    })
  }
  describe(I) {
    return new this.constructor({
      ...this._def,
      description: I
    })
  }
  pipe(I) {
    return N$.create(this, I)
  }
  readonly() {
    return JR.create(this)
  }
  isOptional() {
    return this.safeParse(void 0).success
  }
  isNullable() {
    return this.safeParse(null).success
  }
}
// @from(Start 5971030, End 5971052)
Ys5 = /^c[^\s-]{8,}$/i
// @from(Start 5971056, End 5971075)
_s5 = /^[0-9a-z]+$/
// @from(Start 5971079, End 5971112)
Ds5 = /^[0-9A-HJKMNP-TV-Z]{26}$/i
// @from(Start 5971116, End 5971210)
Hs5 = /^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i
// @from(Start 5971214, End 5971239)
Fs5 = /^[a-z0-9_-]{21}$/i
// @from(Start 5971243, End 5971299)
gs5 = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/
// @from(Start 5971303, End 5971607)
Js5 = /^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/
// @from(Start 5971611, End 5971701)
Ks5 = /^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i
// @from(Start 5971705, End 5971765)
Ns5 = "^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$"
// @from(Start 5971769, End 5971772)
KF1
// @from(Start 5971774, End 5971897)
zs5 = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/
// @from(Start 5971901, End 5972045)
Qs5 = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/
// @from(Start 5972049, End 5972718)
fs5 = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/
// @from(Start 5972722, End 5973425)
qs5 = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/
// @from(Start 5973429, End 5973501)
Rs5 = /^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/
// @from(Start 5973505, End 5973583)
Us5 = /^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/
// @from(Start 5973587, End 5973788)
BW2 = "((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))"
// @from(Start 5973792, End 5973820)
vs5 = new RegExp(`^${BW2}$`)
// @from(Start 5973823, End 5974008)
function AW2(I) {
  let d = "([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";
  if (I.precision) d = `${d}\\.\\d{${I.precision}}`;
  else if (I.precision == null) d = `${d}(\\.\\d+)?`;
  return d
}
// @from(Start 5974010, End 5974064)
function Es5(I) {
  return new RegExp(`^${AW2(I)}$`)
}
// @from(Start 5974066, End 5974263)
function VW2(I) {
  let d = `${BW2}T${AW2(I)}`,
    G = [];
  if (G.push(I.local ? "Z?" : "Z"), I.offset) G.push("([+-]\\d{2}:?\\d{2})");
  return d = `${d}(${G.join("|")})`, new RegExp(`^${d}$`)
}
// @from(Start 5974265, End 5974403)
function Ms5(I, d) {
  if ((d === "v4" || !d) && zs5.test(I)) return !0;
  if ((d === "v6" || !d) && fs5.test(I)) return !0;
  return !1
}
// @from(Start 5974405, End 5974789)
function Ss5(I, d) {
  if (!gs5.test(I)) return !1;
  try {
    let [G] = I.split("."), Z = G.replace(/-/g, "+").replace(/_/g, "/").padEnd(G.length + (4 - G.length % 4) % 4, "="), C = JSON.parse(atob(Z));
    if (typeof C !== "object" || C === null) return !1;
    if (!C.typ || !C.alg) return !1;
    if (d && C.alg !== d) return !1;
    return !0
  } catch (G) {
    return !1
  }
}
// @from(Start 5974791, End 5974929)
function Ls5(I, d) {
  if ((d === "v4" || !d) && Qs5.test(I)) return !0;
  if ((d === "v6" || !d) && qs5.test(I)) return !0;
  return !1
}
// @from(Start 5974930, End 5987459)
class AW extends P4 {
  _parse(I) {
    if (this._def.coerce) I.data = String(I.data);
    if (this._getType(I) !== _2.string) {
      let C = this._getOrReturnCtx(I);
      return d2(C, {
        code: y0.invalid_type,
        expected: _2.string,
        received: C.parsedType
      }), w4
    }
    let G = new h7,
      Z = void 0;
    for (let C of this._def.checks)
      if (C.kind === "min") {
        if (I.data.length < C.value) Z = this._getOrReturnCtx(I, Z), d2(Z, {
          code: y0.too_small,
          minimum: C.value,
          type: "string",
          inclusive: !0,
          exact: !1,
          message: C.message
        }), G.dirty()
      } else if (C.kind === "max") {
      if (I.data.length > C.value) Z = this._getOrReturnCtx(I, Z), d2(Z, {
        code: y0.too_big,
        maximum: C.value,
        type: "string",
        inclusive: !0,
        exact: !1,
        message: C.message
      }), G.dirty()
    } else if (C.kind === "length") {
      let W = I.data.length > C.value,
        w = I.data.length < C.value;
      if (W || w) {
        if (Z = this._getOrReturnCtx(I, Z), W) d2(Z, {
          code: y0.too_big,
          maximum: C.value,
          type: "string",
          inclusive: !0,
          exact: !0,
          message: C.message
        });
        else if (w) d2(Z, {
          code: y0.too_small,
          minimum: C.value,
          type: "string",
          inclusive: !0,
          exact: !0,
          message: C.message
        });
        G.dirty()
      }
    } else if (C.kind === "email") {
      if (!Ks5.test(I.data)) Z = this._getOrReturnCtx(I, Z), d2(Z, {
        validation: "email",
        code: y0.invalid_string,
        message: C.message
      }), G.dirty()
    } else if (C.kind === "emoji") {
      if (!KF1) KF1 = new RegExp(Ns5, "u");
      if (!KF1.test(I.data)) Z = this._getOrReturnCtx(I, Z), d2(Z, {
        validation: "emoji",
        code: y0.invalid_string,
        message: C.message
      }), G.dirty()
    } else if (C.kind === "uuid") {
      if (!Hs5.test(I.data)) Z = this._getOrReturnCtx(I, Z), d2(Z, {
        validation: "uuid",
        code: y0.invalid_string,
        message: C.message
      }), G.dirty()
    } else if (C.kind === "nanoid") {
      if (!Fs5.test(I.data)) Z = this._getOrReturnCtx(I, Z), d2(Z, {
        validation: "nanoid",
        code: y0.invalid_string,
        message: C.message
      }), G.dirty()
    } else if (C.kind === "cuid") {
      if (!Ys5.test(I.data)) Z = this._getOrReturnCtx(I, Z), d2(Z, {
        validation: "cuid",
        code: y0.invalid_string,
        message: C.message
      }), G.dirty()
    } else if (C.kind === "cuid2") {
      if (!_s5.test(I.data)) Z = this._getOrReturnCtx(I, Z), d2(Z, {
        validation: "cuid2",
        code: y0.invalid_string,
        message: C.message
      }), G.dirty()
    } else if (C.kind === "ulid") {
      if (!Ds5.test(I.data)) Z = this._getOrReturnCtx(I, Z), d2(Z, {
        validation: "ulid",
        code: y0.invalid_string,
        message: C.message
      }), G.dirty()
    } else if (C.kind === "url") try {
      new URL(I.data)
    } catch (W) {
      Z = this._getOrReturnCtx(I, Z), d2(Z, {
        validation: "url",
        code: y0.invalid_string,
        message: C.message
      }), G.dirty()
    } else if (C.kind === "regex") {
      if (C.regex.lastIndex = 0, !C.regex.test(I.data)) Z = this._getOrReturnCtx(I, Z), d2(Z, {
        validation: "regex",
        code: y0.invalid_string,
        message: C.message
      }), G.dirty()
    } else if (C.kind === "trim") I.data = I.data.trim();
    else if (C.kind === "includes") {
      if (!I.data.includes(C.value, C.position)) Z = this._getOrReturnCtx(I, Z), d2(Z, {
        code: y0.invalid_string,
        validation: {
          includes: C.value,
          position: C.position
        },
        message: C.message
      }), G.dirty()
    } else if (C.kind === "toLowerCase") I.data = I.data.toLowerCase();
    else if (C.kind === "toUpperCase") I.data = I.data.toUpperCase();
    else if (C.kind === "startsWith") {
      if (!I.data.startsWith(C.value)) Z = this._getOrReturnCtx(I, Z), d2(Z, {
        code: y0.invalid_string,
        validation: {
          startsWith: C.value
        },
        message: C.message
      }), G.dirty()
    } else if (C.kind === "endsWith") {
      if (!I.data.endsWith(C.value)) Z = this._getOrReturnCtx(I, Z), d2(Z, {
        code: y0.invalid_string,
        validation: {
          endsWith: C.value
        },
        message: C.message
      }), G.dirty()
    } else if (C.kind === "datetime") {
      if (!VW2(C).test(I.data)) Z = this._getOrReturnCtx(I, Z), d2(Z, {
        code: y0.invalid_string,
        validation: "datetime",
        message: C.message
      }), G.dirty()
    } else if (C.kind === "date") {
      if (!vs5.test(I.data)) Z = this._getOrReturnCtx(I, Z), d2(Z, {
        code: y0.invalid_string,
        validation: "date",
        message: C.message
      }), G.dirty()
    } else if (C.kind === "time") {
      if (!Es5(C).test(I.data)) Z = this._getOrReturnCtx(I, Z), d2(Z, {
        code: y0.invalid_string,
        validation: "time",
        message: C.message
      }), G.dirty()
    } else if (C.kind === "duration") {
      if (!Js5.test(I.data)) Z = this._getOrReturnCtx(I, Z), d2(Z, {
        validation: "duration",
        code: y0.invalid_string,
        message: C.message
      }), G.dirty()
    } else if (C.kind === "ip") {
      if (!Ms5(I.data, C.version)) Z = this._getOrReturnCtx(I, Z), d2(Z, {
        validation: "ip",
        code: y0.invalid_string,
        message: C.message
      }), G.dirty()
    } else if (C.kind === "jwt") {
      if (!Ss5(I.data, C.alg)) Z = this._getOrReturnCtx(I, Z), d2(Z, {
        validation: "jwt",
        code: y0.invalid_string,
        message: C.message
      }), G.dirty()
    } else if (C.kind === "cidr") {
      if (!Ls5(I.data, C.version)) Z = this._getOrReturnCtx(I, Z), d2(Z, {
        validation: "cidr",
        code: y0.invalid_string,
        message: C.message
      }), G.dirty()
    } else if (C.kind === "base64") {
      if (!Rs5.test(I.data)) Z = this._getOrReturnCtx(I, Z), d2(Z, {
        validation: "base64",
        code: y0.invalid_string,
        message: C.message
      }), G.dirty()
    } else if (C.kind === "base64url") {
      if (!Us5.test(I.data)) Z = this._getOrReturnCtx(I, Z), d2(Z, {
        validation: "base64url",
        code: y0.invalid_string,
        message: C.message
      }), G.dirty()
    } else N5.assertNever(C);
    return {
      status: G.value,
      value: I.data
    }
  }
  _regex(I, d, G) {
    return this.refinement((Z) => I.test(Z), {
      validation: d,
      code: y0.invalid_string,
      ...M2.errToObj(G)
    })
  }
  _addCheck(I) {
    return new AW({
      ...this._def,
      checks: [...this._def.checks, I]
    })
  }
  email(I) {
    return this._addCheck({
      kind: "email",
      ...M2.errToObj(I)
    })
  }
  url(I) {
    return this._addCheck({
      kind: "url",
      ...M2.errToObj(I)
    })
  }
  emoji(I) {
    return this._addCheck({
      kind: "emoji",
      ...M2.errToObj(I)
    })
  }
  uuid(I) {
    return this._addCheck({
      kind: "uuid",
      ...M2.errToObj(I)
    })
  }
  nanoid(I) {
    return this._addCheck({
      kind: "nanoid",
      ...M2.errToObj(I)
    })
  }
  cuid(I) {
    return this._addCheck({
      kind: "cuid",
      ...M2.errToObj(I)
    })
  }
  cuid2(I) {
    return this._addCheck({
      kind: "cuid2",
      ...M2.errToObj(I)
    })
  }
  ulid(I) {
    return this._addCheck({
      kind: "ulid",
      ...M2.errToObj(I)
    })
  }
  base64(I) {
    return this._addCheck({
      kind: "base64",
      ...M2.errToObj(I)
    })
  }
  base64url(I) {
    return this._addCheck({
      kind: "base64url",
      ...M2.errToObj(I)
    })
  }
  jwt(I) {
    return this._addCheck({
      kind: "jwt",
      ...M2.errToObj(I)
    })
  }
  ip(I) {
    return this._addCheck({
      kind: "ip",
      ...M2.errToObj(I)
    })
  }
  cidr(I) {
    return this._addCheck({
      kind: "cidr",
      ...M2.errToObj(I)
    })
  }
  datetime(I) {
    var d, G;
    if (typeof I === "string") return this._addCheck({
      kind: "datetime",
      precision: null,
      offset: !1,
      local: !1,
      message: I
    });
    return this._addCheck({
      kind: "datetime",
      precision: typeof(I === null || I === void 0 ? void 0 : I.precision) === "undefined" ? null : I === null || I === void 0 ? void 0 : I.precision,
      offset: (d = I === null || I === void 0 ? void 0 : I.offset) !== null && d !== void 0 ? d : !1,
      local: (G = I === null || I === void 0 ? void 0 : I.local) !== null && G !== void 0 ? G : !1,
      ...M2.errToObj(I === null || I === void 0 ? void 0 : I.message)
    })
  }
  date(I) {
    return this._addCheck({
      kind: "date",
      message: I
    })
  }
  time(I) {
    if (typeof I === "string") return this._addCheck({
      kind: "time",
      precision: null,
      message: I
    });
    return this._addCheck({
      kind: "time",
      precision: typeof(I === null || I === void 0 ? void 0 : I.precision) === "undefined" ? null : I === null || I === void 0 ? void 0 : I.precision,
      ...M2.errToObj(I === null || I === void 0 ? void 0 : I.message)
    })
  }
  duration(I) {
    return this._addCheck({
      kind: "duration",
      ...M2.errToObj(I)
    })
  }
  regex(I, d) {
    return this._addCheck({
      kind: "regex",
      regex: I,
      ...M2.errToObj(d)
    })
  }
  includes(I, d) {
    return this._addCheck({
      kind: "includes",
      value: I,
      position: d === null || d === void 0 ? void 0 : d.position,
      ...M2.errToObj(d === null || d === void 0 ? void 0 : d.message)
    })
  }
  startsWith(I, d) {
    return this._addCheck({
      kind: "startsWith",
      value: I,
      ...M2.errToObj(d)
    })
  }
  endsWith(I, d) {
    return this._addCheck({
      kind: "endsWith",
      value: I,
      ...M2.errToObj(d)
    })
  }
  min(I, d) {
    return this._addCheck({
      kind: "min",
      value: I,
      ...M2.errToObj(d)
    })
  }
  max(I, d) {
    return this._addCheck({
      kind: "max",
      value: I,
      ...M2.errToObj(d)
    })
  }
  length(I, d) {
    return this._addCheck({
      kind: "length",
      value: I,
      ...M2.errToObj(d)
    })
  }
  nonempty(I) {
    return this.min(1, M2.errToObj(I))
  }
  trim() {
    return new AW({
      ...this._def,
      checks: [...this._def.checks, {
        kind: "trim"
      }]
    })
  }
  toLowerCase() {
    return new AW({
      ...this._def,
      checks: [...this._def.checks, {
        kind: "toLowerCase"
      }]
    })
  }
  toUpperCase() {
    return new AW({
      ...this._def,
      checks: [...this._def.checks, {
        kind: "toUpperCase"
      }]
    })
  }
  get isDatetime() {
    return !!this._def.checks.find((I) => I.kind === "datetime")
  }
  get isDate() {
    return !!this._def.checks.find((I) => I.kind === "date")
  }
  get isTime() {
    return !!this._def.checks.find((I) => I.kind === "time")
  }
  get isDuration() {
    return !!this._def.checks.find((I) => I.kind === "duration")
  }
  get isEmail() {
    return !!this._def.checks.find((I) => I.kind === "email")
  }
  get isURL() {
    return !!this._def.checks.find((I) => I.kind === "url")
  }
  get isEmoji() {
    return !!this._def.checks.find((I) => I.kind === "emoji")
  }
  get isUUID() {
    return !!this._def.checks.find((I) => I.kind === "uuid")
  }
  get isNANOID() {
    return !!this._def.checks.find((I) => I.kind === "nanoid")
  }
  get isCUID() {
    return !!this._def.checks.find((I) => I.kind === "cuid")
  }
  get isCUID2() {
    return !!this._def.checks.find((I) => I.kind === "cuid2")
  }
  get isULID() {
    return !!this._def.checks.find((I) => I.kind === "ulid")
  }
  get isIP() {
    return !!this._def.checks.find((I) => I.kind === "ip")
  }
  get isCIDR() {
    return !!this._def.checks.find((I) => I.kind === "cidr")
  }
  get isBase64() {
    return !!this._def.checks.find((I) => I.kind === "base64")
  }
  get isBase64url() {
    return !!this._def.checks.find((I) => I.kind === "base64url")
  }
  get minLength() {
    let I = null;
    for (let d of this._def.checks)
      if (d.kind === "min") {
        if (I === null || d.value > I) I = d.value
      } return I
  }
  get maxLength() {
    let I = null;
    for (let d of this._def.checks)
      if (d.kind === "max") {
        if (I === null || d.value < I) I = d.value
      } return I
  }
}
// @from(Start 5987674, End 5987954)
function ys5(I, d) {
  let G = (I.toString().split(".")[1] || "").length,
    Z = (d.toString().split(".")[1] || "").length,
    C = G > Z ? G : Z,
    W = parseInt(I.toFixed(C).replace(".", "")),
    w = parseInt(d.toFixed(C).replace(".", ""));
  return W % w / Math.pow(10, C)
}
// @from(Start 5987955, End 5992532)
class tD extends P4 {
  constructor() {
    super(...arguments);
    this.min = this.gte, this.max = this.lte, this.step = this.multipleOf
  }
  _parse(I) {
    if (this._def.coerce) I.data = Number(I.data);
    if (this._getType(I) !== _2.number) {
      let C = this._getOrReturnCtx(I);
      return d2(C, {
        code: y0.invalid_type,
        expected: _2.number,
        received: C.parsedType
      }), w4
    }
    let G = void 0,
      Z = new h7;
    for (let C of this._def.checks)
      if (C.kind === "int") {
        if (!N5.isInteger(I.data)) G = this._getOrReturnCtx(I, G), d2(G, {
          code: y0.invalid_type,
          expected: "integer",
          received: "float",
          message: C.message
        }), Z.dirty()
      } else if (C.kind === "min") {
      if (C.inclusive ? I.data < C.value : I.data <= C.value) G = this._getOrReturnCtx(I, G), d2(G, {
        code: y0.too_small,
        minimum: C.value,
        type: "number",
        inclusive: C.inclusive,
        exact: !1,
        message: C.message
      }), Z.dirty()
    } else if (C.kind === "max") {
      if (C.inclusive ? I.data > C.value : I.data >= C.value) G = this._getOrReturnCtx(I, G), d2(G, {
        code: y0.too_big,
        maximum: C.value,
        type: "number",
        inclusive: C.inclusive,
        exact: !1,
        message: C.message
      }), Z.dirty()
    } else if (C.kind === "multipleOf") {
      if (ys5(I.data, C.value) !== 0) G = this._getOrReturnCtx(I, G), d2(G, {
        code: y0.not_multiple_of,
        multipleOf: C.value,
        message: C.message
      }), Z.dirty()
    } else if (C.kind === "finite") {
      if (!Number.isFinite(I.data)) G = this._getOrReturnCtx(I, G), d2(G, {
        code: y0.not_finite,
        message: C.message
      }), Z.dirty()
    } else N5.assertNever(C);
    return {
      status: Z.value,
      value: I.data
    }
  }
  gte(I, d) {
    return this.setLimit("min", I, !0, M2.toString(d))
  }
  gt(I, d) {
    return this.setLimit("min", I, !1, M2.toString(d))
  }
  lte(I, d) {
    return this.setLimit("max", I, !0, M2.toString(d))
  }
  lt(I, d) {
    return this.setLimit("max", I, !1, M2.toString(d))
  }
  setLimit(I, d, G, Z) {
    return new tD({
      ...this._def,
      checks: [...this._def.checks, {
        kind: I,
        value: d,
        inclusive: G,
        message: M2.toString(Z)
      }]
    })
  }
  _addCheck(I) {
    return new tD({
      ...this._def,
      checks: [...this._def.checks, I]
    })
  }
  int(I) {
    return this._addCheck({
      kind: "int",
      message: M2.toString(I)
    })
  }
  positive(I) {
    return this._addCheck({
      kind: "min",
      value: 0,
      inclusive: !1,
      message: M2.toString(I)
    })
  }
  negative(I) {
    return this._addCheck({
      kind: "max",
      value: 0,
      inclusive: !1,
      message: M2.toString(I)
    })
  }
  nonpositive(I) {
    return this._addCheck({
      kind: "max",
      value: 0,
      inclusive: !0,
      message: M2.toString(I)
    })
  }
  nonnegative(I) {
    return this._addCheck({
      kind: "min",
      value: 0,
      inclusive: !0,
      message: M2.toString(I)
    })
  }
  multipleOf(I, d) {
    return this._addCheck({
      kind: "multipleOf",
      value: I,
      message: M2.toString(d)
    })
  }
  finite(I) {
    return this._addCheck({
      kind: "finite",
      message: M2.toString(I)
    })
  }
  safe(I) {
    return this._addCheck({
      kind: "min",
      inclusive: !0,
      value: Number.MIN_SAFE_INTEGER,
      message: M2.toString(I)
    })._addCheck({
      kind: "max",
      inclusive: !0,
      value: Number.MAX_SAFE_INTEGER,
      message: M2.toString(I)
    })
  }
  get minValue() {
    let I = null;
    for (let d of this._def.checks)
      if (d.kind === "min") {
        if (I === null || d.value > I) I = d.value
      } return I
  }
  get maxValue() {
    let I = null;
    for (let d of this._def.checks)
      if (d.kind === "max") {
        if (I === null || d.value < I) I = d.value
      } return I
  }
  get isInt() {
    return !!this._def.checks.find((I) => I.kind === "int" || I.kind === "multipleOf" && N5.isInteger(I.value))
  }
  get isFinite() {
    let I = null,
      d = null;
    for (let G of this._def.checks)
      if (G.kind === "finite" || G.kind === "int" || G.kind === "multipleOf") return !0;
      else if (G.kind === "min") {
      if (d === null || G.value > d) d = G.value
    } else if (G.kind === "max") {
      if (I === null || G.value < I) I = G.value
    }
    return Number.isFinite(d) && Number.isFinite(I)
  }
}
// @from(Start 5992705, End 5995905)
class IH extends P4 {
  constructor() {
    super(...arguments);
    this.min = this.gte, this.max = this.lte
  }
  _parse(I) {
    if (this._def.coerce) try {
      I.data = BigInt(I.data)
    } catch (C) {
      return this._getInvalidInput(I)
    }
    if (this._getType(I) !== _2.bigint) return this._getInvalidInput(I);
    let G = void 0,
      Z = new h7;
    for (let C of this._def.checks)
      if (C.kind === "min") {
        if (C.inclusive ? I.data < C.value : I.data <= C.value) G = this._getOrReturnCtx(I, G), d2(G, {
          code: y0.too_small,
          type: "bigint",
          minimum: C.value,
          inclusive: C.inclusive,
          message: C.message
        }), Z.dirty()
      } else if (C.kind === "max") {
      if (C.inclusive ? I.data > C.value : I.data >= C.value) G = this._getOrReturnCtx(I, G), d2(G, {
        code: y0.too_big,
        type: "bigint",
        maximum: C.value,
        inclusive: C.inclusive,
        message: C.message
      }), Z.dirty()
    } else if (C.kind === "multipleOf") {
      if (I.data % C.value !== BigInt(0)) G = this._getOrReturnCtx(I, G), d2(G, {
        code: y0.not_multiple_of,
        multipleOf: C.value,
        message: C.message
      }), Z.dirty()
    } else N5.assertNever(C);
    return {
      status: Z.value,
      value: I.data
    }
  }
  _getInvalidInput(I) {
    let d = this._getOrReturnCtx(I);
    return d2(d, {
      code: y0.invalid_type,
      expected: _2.bigint,
      received: d.parsedType
    }), w4
  }
  gte(I, d) {
    return this.setLimit("min", I, !0, M2.toString(d))
  }
  gt(I, d) {
    return this.setLimit("min", I, !1, M2.toString(d))
  }
  lte(I, d) {
    return this.setLimit("max", I, !0, M2.toString(d))
  }
  lt(I, d) {
    return this.setLimit("max", I, !1, M2.toString(d))
  }
  setLimit(I, d, G, Z) {
    return new IH({
      ...this._def,
      checks: [...this._def.checks, {
        kind: I,
        value: d,
        inclusive: G,
        message: M2.toString(Z)
      }]
    })
  }
  _addCheck(I) {
    return new IH({
      ...this._def,
      checks: [...this._def.checks, I]
    })
  }
  positive(I) {
    return this._addCheck({
      kind: "min",
      value: BigInt(0),
      inclusive: !1,
      message: M2.toString(I)
    })
  }
  negative(I) {
    return this._addCheck({
      kind: "max",
      value: BigInt(0),
      inclusive: !1,
      message: M2.toString(I)
    })
  }
  nonpositive(I) {
    return this._addCheck({
      kind: "max",
      value: BigInt(0),
      inclusive: !0,
      message: M2.toString(I)
    })
  }
  nonnegative(I) {
    return this._addCheck({
      kind: "min",
      value: BigInt(0),
      inclusive: !0,
      message: M2.toString(I)
    })
  }
  multipleOf(I, d) {
    return this._addCheck({
      kind: "multipleOf",
      value: I,
      message: M2.toString(d)
    })
  }
  get minValue() {
    let I = null;
    for (let d of this._def.checks)
      if (d.kind === "min") {
        if (I === null || d.value > I) I = d.value
      } return I
  }
  get maxValue() {
    let I = null;
    for (let d of this._def.checks)
      if (d.kind === "max") {
        if (I === null || d.value < I) I = d.value
      } return I
  }
}
// @from(Start 5996119, End 5996448)
class BR extends P4 {
  _parse(I) {
    if (this._def.coerce) I.data = Boolean(I.data);
    if (this._getType(I) !== _2.boolean) {
      let G = this._getOrReturnCtx(I);
      return d2(G, {
        code: y0.invalid_type,
        expected: _2.boolean,
        received: G.parsedType
      }), w4
    }
    return DI(I.data)
  }
}
// @from(Start 5996606, End 5998574)
class lJ extends P4 {
  _parse(I) {
    if (this._def.coerce) I.data = new Date(I.data);
    if (this._getType(I) !== _2.date) {
      let C = this._getOrReturnCtx(I);
      return d2(C, {
        code: y0.invalid_type,
        expected: _2.date,
        received: C.parsedType
      }), w4
    }
    if (isNaN(I.data.getTime())) {
      let C = this._getOrReturnCtx(I);
      return d2(C, {
        code: y0.invalid_date
      }), w4
    }
    let G = new h7,
      Z = void 0;
    for (let C of this._def.checks)
      if (C.kind === "min") {
        if (I.data.getTime() < C.value) Z = this._getOrReturnCtx(I, Z), d2(Z, {
          code: y0.too_small,
          message: C.message,
          inclusive: !0,
          exact: !1,
          minimum: C.value,
          type: "date"
        }), G.dirty()
      } else if (C.kind === "max") {
      if (I.data.getTime() > C.value) Z = this._getOrReturnCtx(I, Z), d2(Z, {
        code: y0.too_big,
        message: C.message,
        inclusive: !0,
        exact: !1,
        maximum: C.value,
        type: "date"
      }), G.dirty()
    } else N5.assertNever(C);
    return {
      status: G.value,
      value: new Date(I.data.getTime())
    }
  }
  _addCheck(I) {
    return new lJ({
      ...this._def,
      checks: [...this._def.checks, I]
    })
  }
  min(I, d) {
    return this._addCheck({
      kind: "min",
      value: I.getTime(),
      message: M2.toString(d)
    })
  }
  max(I, d) {
    return this._addCheck({
      kind: "max",
      value: I.getTime(),
      message: M2.toString(d)
    })
  }
  get minDate() {
    let I = null;
    for (let d of this._def.checks)
      if (d.kind === "min") {
        if (I === null || d.value > I) I = d.value
      } return I != null ? new Date(I) : null
  }
  get maxDate() {
    let I = null;
    for (let d of this._def.checks)
      if (d.kind === "max") {
        if (I === null || d.value < I) I = d.value
      } return I != null ? new Date(I) : null
  }
}
// @from(Start 5998745, End 5999020)
class H$ extends P4 {
  _parse(I) {
    if (this._getType(I) !== _2.symbol) {
      let G = this._getOrReturnCtx(I);
      return d2(G, {
        code: y0.invalid_type,
        expected: _2.symbol,
        received: G.parsedType
      }), w4
    }
    return DI(I.data)
  }
}
// @from(Start 5999109, End 5999390)
class AR extends P4 {
  _parse(I) {
    if (this._getType(I) !== _2.undefined) {
      let G = this._getOrReturnCtx(I);
      return d2(G, {
        code: y0.invalid_type,
        expected: _2.undefined,
        received: G.parsedType
      }), w4
    }
    return DI(I.data)
  }
}
// @from(Start 5999482, End 5999753)
class VR extends P4 {
  _parse(I) {
    if (this._getType(I) !== _2.null) {
      let G = this._getOrReturnCtx(I);
      return d2(G, {
        code: y0.invalid_type,
        expected: _2.null,
        received: G.parsedType
      }), w4
    }
    return DI(I.data)
  }
}
// @from(Start 5999840, End 5999969)
class bJ extends P4 {
  constructor() {
    super(...arguments);
    this._any = !0
  }
  _parse(I) {
    return DI(I.data)
  }
}
// @from(Start 6000055, End 6000188)
class eD extends P4 {
  constructor() {
    super(...arguments);
    this._unknown = !0
  }
  _parse(I) {
    return DI(I.data)
  }
}
// @from(Start 6000278, End 6000470)
class EB extends P4 {
  _parse(I) {
    let d = this._getOrReturnCtx(I);
    return d2(d, {
      code: y0.invalid_type,
      expected: _2.never,
      received: d.parsedType
    }), w4
  }
}
// @from(Start 6000558, End 6000834)
class F$ extends P4 {
  _parse(I) {
    if (this._getType(I) !== _2.undefined) {
      let G = this._getOrReturnCtx(I);
      return d2(G, {
        code: y0.invalid_type,
        expected: _2.void,
        received: G.parsedType
      }), w4
    }
    return DI(I.data)
  }
}
// @from(Start 6000921, End 6003040)
class VW extends P4 {
  _parse(I) {
    let {
      ctx: d,
      status: G
    } = this._processInputParams(I), Z = this._def;
    if (d.parsedType !== _2.array) return d2(d, {
      code: y0.invalid_type,
      expected: _2.array,
      received: d.parsedType
    }), w4;
    if (Z.exactLength !== null) {
      let W = d.data.length > Z.exactLength.value,
        w = d.data.length < Z.exactLength.value;
      if (W || w) d2(d, {
        code: W ? y0.too_big : y0.too_small,
        minimum: w ? Z.exactLength.value : void 0,
        maximum: W ? Z.exactLength.value : void 0,
        type: "array",
        inclusive: !0,
        exact: !0,
        message: Z.exactLength.message
      }), G.dirty()
    }
    if (Z.minLength !== null) {
      if (d.data.length < Z.minLength.value) d2(d, {
        code: y0.too_small,
        minimum: Z.minLength.value,
        type: "array",
        inclusive: !0,
        exact: !1,
        message: Z.minLength.message
      }), G.dirty()
    }
    if (Z.maxLength !== null) {
      if (d.data.length > Z.maxLength.value) d2(d, {
        code: y0.too_big,
        maximum: Z.maxLength.value,
        type: "array",
        inclusive: !0,
        exact: !1,
        message: Z.maxLength.message
      }), G.dirty()
    }
    if (d.common.async) return Promise.all([...d.data].map((W, w) => {
      return Z.type._parseAsync(new XW(d, W, d.path, w))
    })).then((W) => {
      return h7.mergeArray(G, W)
    });
    let C = [...d.data].map((W, w) => {
      return Z.type._parseSync(new XW(d, W, d.path, w))
    });
    return h7.mergeArray(G, C)
  }
  get element() {
    return this._def.type
  }
  min(I, d) {
    return new VW({
      ...this._def,
      minLength: {
        value: I,
        message: M2.toString(d)
      }
    })
  }
  max(I, d) {
    return new VW({
      ...this._def,
      maxLength: {
        value: I,
        message: M2.toString(d)
      }
    })
  }
  length(I, d) {
    return new VW({
      ...this._def,
      exactLength: {
        value: I,
        message: M2.toString(d)
      }
    })
  }
  nonempty(I) {
    return this.min(1, I)
  }
}
// @from(Start 6003210, End 6003729)
function ZR(I) {
  if (I instanceof M3) {
    let d = {};
    for (let G in I.shape) {
      let Z = I.shape[G];
      d[G] = VG.create(ZR(Z))
    }
    return new M3({
      ...I._def,
      shape: () => d
    })
  } else if (I instanceof VW) return new VW({
    ...I._def,
    type: ZR(I.element)
  });
  else if (I instanceof VG) return VG.create(ZR(I.unwrap()));
  else if (I instanceof GX) return GX.create(ZR(I.unwrap()));
  else if (I instanceof MB) return MB.create(I.items.map((d) => ZR(d)));
  else return I
}
// @from(Start 6003730, End 6008568)
class M3 extends P4 {
  constructor() {
    super(...arguments);
    this._cached = null, this.nonstrict = this.passthrough, this.augment = this.extend
  }
  _getCached() {
    if (this._cached !== null) return this._cached;
    let I = this._def.shape(),
      d = N5.objectKeys(I);
    return this._cached = {
      shape: I,
      keys: d
    }
  }
  _parse(I) {
    if (this._getType(I) !== _2.object) {
      let A = this._getOrReturnCtx(I);
      return d2(A, {
        code: y0.invalid_type,
        expected: _2.object,
        received: A.parsedType
      }), w4
    }
    let {
      status: G,
      ctx: Z
    } = this._processInputParams(I), {
      shape: C,
      keys: W
    } = this._getCached(), w = [];
    if (!(this._def.catchall instanceof EB && this._def.unknownKeys === "strip")) {
      for (let A in Z.data)
        if (!W.includes(A)) w.push(A)
    }
    let B = [];
    for (let A of W) {
      let V = C[A],
        X = Z.data[A];
      B.push({
        key: {
          status: "valid",
          value: A
        },
        value: V._parse(new XW(Z, X, Z.path, A)),
        alwaysSet: A in Z.data
      })
    }
    if (this._def.catchall instanceof EB) {
      let A = this._def.unknownKeys;
      if (A === "passthrough")
        for (let V of w) B.push({
          key: {
            status: "valid",
            value: V
          },
          value: {
            status: "valid",
            value: Z.data[V]
          }
        });
      else if (A === "strict") {
        if (w.length > 0) d2(Z, {
          code: y0.unrecognized_keys,
          keys: w
        }), G.dirty()
      } else if (A === "strip");
      else throw new Error("Internal ZodObject error: invalid unknownKeys value.")
    } else {
      let A = this._def.catchall;
      for (let V of w) {
        let X = Z.data[V];
        B.push({
          key: {
            status: "valid",
            value: V
          },
          value: A._parse(new XW(Z, X, Z.path, V)),
          alwaysSet: V in Z.data
        })
      }
    }
    if (Z.common.async) return Promise.resolve().then(async () => {
      let A = [];
      for (let V of B) {
        let X = await V.key,
          _ = await V.value;
        A.push({
          key: X,
          value: _,
          alwaysSet: V.alwaysSet
        })
      }
      return A
    }).then((A) => {
      return h7.mergeObjectSync(G, A)
    });
    else return h7.mergeObjectSync(G, B)
  }
  get shape() {
    return this._def.shape()
  }
  strict(I) {
    return M2.errToObj, new M3({
      ...this._def,
      unknownKeys: "strict",
      ...I !== void 0 ? {
        errorMap: (d, G) => {
          var Z, C, W, w;
          let B = (W = (C = (Z = this._def).errorMap) === null || C === void 0 ? void 0 : C.call(Z, d, G).message) !== null && W !== void 0 ? W : G.defaultError;
          if (d.code === "unrecognized_keys") return {
            message: (w = M2.errToObj(I).message) !== null && w !== void 0 ? w : B
          };
          return {
            message: B
          }
        }
      } : {}
    })
  }
  strip() {
    return new M3({
      ...this._def,
      unknownKeys: "strip"
    })
  }
  passthrough() {
    return new M3({
      ...this._def,
      unknownKeys: "passthrough"
    })
  }
  extend(I) {
    return new M3({
      ...this._def,
      shape: () => ({
        ...this._def.shape(),
        ...I
      })
    })
  }
  merge(I) {
    return new M3({
      unknownKeys: I._def.unknownKeys,
      catchall: I._def.catchall,
      shape: () => ({
        ...this._def.shape(),
        ...I._def.shape()
      }),
      typeName: T0.ZodObject
    })
  }
  setKey(I, d) {
    return this.augment({
      [I]: d
    })
  }
  catchall(I) {
    return new M3({
      ...this._def,
      catchall: I
    })
  }
  pick(I) {
    let d = {};
    return N5.objectKeys(I).forEach((G) => {
      if (I[G] && this.shape[G]) d[G] = this.shape[G]
    }), new M3({
      ...this._def,
      shape: () => d
    })
  }
  omit(I) {
    let d = {};
    return N5.objectKeys(this.shape).forEach((G) => {
      if (!I[G]) d[G] = this.shape[G]
    }), new M3({
      ...this._def,
      shape: () => d
    })
  }
  deepPartial() {
    return ZR(this)
  }
  partial(I) {
    let d = {};
    return N5.objectKeys(this.shape).forEach((G) => {
      let Z = this.shape[G];
      if (I && !I[G]) d[G] = Z;
      else d[G] = Z.optional()
    }), new M3({
      ...this._def,
      shape: () => d
    })
  }
  required(I) {
    let d = {};
    return N5.objectKeys(this.shape).forEach((G) => {
      if (I && !I[G]) d[G] = this.shape[G];
      else {
        let C = this.shape[G];
        while (C instanceof VG) C = C._def.innerType;
        d[G] = C
      }
    }), new M3({
      ...this._def,
      shape: () => d
    })
  }
  keyof() {
    return XW2(N5.objectKeys(this.shape))
  }
}
// @from(Start 6009066, End 6010768)
class XR extends P4 {
  _parse(I) {
    let {
      ctx: d
    } = this._processInputParams(I), G = this._def.options;

    function Z(C) {
      for (let w of C)
        if (w.result.status === "valid") return w.result;
      for (let w of C)
        if (w.result.status === "dirty") return d.common.issues.push(...w.ctx.common.issues), w.result;
      let W = C.map((w) => new AG(w.ctx.common.issues));
      return d2(d, {
        code: y0.invalid_union,
        unionErrors: W
      }), w4
    }
    if (d.common.async) return Promise.all(G.map(async (C) => {
      let W = {
        ...d,
        common: {
          ...d.common,
          issues: []
        },
        parent: null
      };
      return {
        result: await C._parseAsync({
          data: d.data,
          path: d.path,
          parent: W
        }),
        ctx: W
      }
    })).then(Z);
    else {
      let C = void 0,
        W = [];
      for (let B of G) {
        let A = {
            ...d,
            common: {
              ...d.common,
              issues: []
            },
            parent: null
          },
          V = B._parseSync({
            data: d.data,
            path: d.path,
            parent: A
          });
        if (V.status === "valid") return V;
        else if (V.status === "dirty" && !C) C = {
          result: V,
          ctx: A
        };
        if (A.common.issues.length) W.push(A.common.issues)
      }
      if (C) return d.common.issues.push(...C.ctx.common.issues), C.result;
      let w = W.map((B) => new AG(B));
      return d2(d, {
        code: y0.invalid_union,
        unionErrors: w
      }), w4
    }
  }
  get options() {
    return this._def.options
  }
}
// @from(Start 6010879, End 6011591)
IX = (I) => {
  if (I instanceof _R) return IX(I.schema);
  else if (I instanceof bZ) return IX(I.innerType());
  else if (I instanceof DR) return [I.value];
  else if (I instanceof dH) return I.options;
  else if (I instanceof HR) return N5.objectValues(I.enum);
  else if (I instanceof FR) return IX(I._def.innerType);
  else if (I instanceof AR) return [void 0];
  else if (I instanceof VR) return [null];
  else if (I instanceof VG) return [void 0, ...IX(I.unwrap())];
  else if (I instanceof GX) return [null, ...IX(I.unwrap())];
  else if (I instanceof Ka) return IX(I.unwrap());
  else if (I instanceof JR) return IX(I.unwrap());
  else if (I instanceof gR) return IX(I._def.innerType);
  else return []
}
// @from(Start 6011593, End 6013004)
class Ja extends P4 {
  _parse(I) {
    let {
      ctx: d
    } = this._processInputParams(I);
    if (d.parsedType !== _2.object) return d2(d, {
      code: y0.invalid_type,
      expected: _2.object,
      received: d.parsedType
    }), w4;
    let G = this.discriminator,
      Z = d.data[G],
      C = this.optionsMap.get(Z);
    if (!C) return d2(d, {
      code: y0.invalid_union_discriminator,
      options: Array.from(this.optionsMap.keys()),
      path: [G]
    }), w4;
    if (d.common.async) return C._parseAsync({
      data: d.data,
      path: d.path,
      parent: d
    });
    else return C._parseSync({
      data: d.data,
      path: d.path,
      parent: d
    })
  }
  get discriminator() {
    return this._def.discriminator
  }
  get options() {
    return this._def.options
  }
  get optionsMap() {
    return this._def.optionsMap
  }
  static create(I, d, G) {
    let Z = new Map;
    for (let C of d) {
      let W = IX(C.shape[I]);
      if (!W.length) throw new Error(`A discriminator value for key \`${I}\` could not be extracted from all schema options`);
      for (let w of W) {
        if (Z.has(w)) throw new Error(`Discriminator property ${String(I)} has duplicate value ${String(w)}`);
        Z.set(w, C)
      }
    }
    return new Ja({
      typeName: T0.ZodDiscriminatedUnion,
      discriminator: I,
      options: d,
      optionsMap: Z,
      ...S4(G)
    })
  }
}
// @from(Start 6013006, End 6013988)
function fF1(I, d) {
  let G = dX(I),
    Z = dX(d);
  if (I === d) return {
    valid: !0,
    data: I
  };
  else if (G === _2.object && Z === _2.object) {
    let C = N5.objectKeys(d),
      W = N5.objectKeys(I).filter((B) => C.indexOf(B) !== -1),
      w = {
        ...I,
        ...d
      };
    for (let B of W) {
      let A = fF1(I[B], d[B]);
      if (!A.valid) return {
        valid: !1
      };
      w[B] = A.data
    }
    return {
      valid: !0,
      data: w
    }
  } else if (G === _2.array && Z === _2.array) {
    if (I.length !== d.length) return {
      valid: !1
    };
    let C = [];
    for (let W = 0; W < I.length; W++) {
      let w = I[W],
        B = d[W],
        A = fF1(w, B);
      if (!A.valid) return {
        valid: !1
      };
      C.push(A.data)
    }
    return {
      valid: !0,
      data: C
    }
  } else if (G === _2.date && Z === _2.date && +I === +d) return {
    valid: !0,
    data: I
  };
  else return {
    valid: !1
  }
}
// @from(Start 6013989, End 6014870)
class YR extends P4 {
  _parse(I) {
    let {
      status: d,
      ctx: G
    } = this._processInputParams(I), Z = (C, W) => {
      if (zF1(C) || zF1(W)) return w4;
      let w = fF1(C.value, W.value);
      if (!w.valid) return d2(G, {
        code: y0.invalid_intersection_types
      }), w4;
      if (QF1(C) || QF1(W)) d.dirty();
      return {
        status: d.value,
        value: w.data
      }
    };
    if (G.common.async) return Promise.all([this._def.left._parseAsync({
      data: G.data,
      path: G.path,
      parent: G
    }), this._def.right._parseAsync({
      data: G.data,
      path: G.path,
      parent: G
    })]).then(([C, W]) => Z(C, W));
    else return Z(this._def.left._parseSync({
      data: G.data,
      path: G.path,
      parent: G
    }), this._def.right._parseSync({
      data: G.data,
      path: G.path,
      parent: G
    }))
  }
}
// @from(Start 6014998, End 6016129)
class MB extends P4 {
  _parse(I) {
    let {
      status: d,
      ctx: G
    } = this._processInputParams(I);
    if (G.parsedType !== _2.array) return d2(G, {
      code: y0.invalid_type,
      expected: _2.array,
      received: G.parsedType
    }), w4;
    if (G.data.length < this._def.items.length) return d2(G, {
      code: y0.too_small,
      minimum: this._def.items.length,
      inclusive: !0,
      exact: !1,
      type: "array"
    }), w4;
    if (!this._def.rest && G.data.length > this._def.items.length) d2(G, {
      code: y0.too_big,
      maximum: this._def.items.length,
      inclusive: !0,
      exact: !1,
      type: "array"
    }), d.dirty();
    let C = [...G.data].map((W, w) => {
      let B = this._def.items[w] || this._def.rest;
      if (!B) return null;
      return B._parse(new XW(G, W, G.path, w))
    }).filter((W) => !!W);
    if (G.common.async) return Promise.all(C).then((W) => {
      return h7.mergeArray(d, W)
    });
    else return h7.mergeArray(d, C)
  }
  get items() {
    return this._def.items
  }
  rest(I) {
    return new MB({
      ...this._def,
      rest: I
    })
  }
}
// @from(Start 6016349, End 6017410)
class g$ extends P4 {
  get keySchema() {
    return this._def.keyType
  }
  get valueSchema() {
    return this._def.valueType
  }
  _parse(I) {
    let {
      status: d,
      ctx: G
    } = this._processInputParams(I);
    if (G.parsedType !== _2.object) return d2(G, {
      code: y0.invalid_type,
      expected: _2.object,
      received: G.parsedType
    }), w4;
    let Z = [],
      C = this._def.keyType,
      W = this._def.valueType;
    for (let w in G.data) Z.push({
      key: C._parse(new XW(G, w, G.path, w)),
      value: W._parse(new XW(G, G.data[w], G.path, w)),
      alwaysSet: w in G.data
    });
    if (G.common.async) return h7.mergeObjectAsync(d, Z);
    else return h7.mergeObjectSync(d, Z)
  }
  get element() {
    return this._def.valueType
  }
  static create(I, d, G) {
    if (d instanceof P4) return new g$({
      keyType: I,
      valueType: d,
      typeName: T0.ZodRecord,
      ...S4(G)
    });
    return new g$({
      keyType: AW.create(),
      valueType: I,
      typeName: T0.ZodRecord,
      ...S4(d)
    })
  }
}
// @from(Start 6017411, End 6018894)
class J$ extends P4 {
  get keySchema() {
    return this._def.keyType
  }
  get valueSchema() {
    return this._def.valueType
  }
  _parse(I) {
    let {
      status: d,
      ctx: G
    } = this._processInputParams(I);
    if (G.parsedType !== _2.map) return d2(G, {
      code: y0.invalid_type,
      expected: _2.map,
      received: G.parsedType
    }), w4;
    let Z = this._def.keyType,
      C = this._def.valueType,
      W = [...G.data.entries()].map(([w, B], A) => {
        return {
          key: Z._parse(new XW(G, w, G.path, [A, "key"])),
          value: C._parse(new XW(G, B, G.path, [A, "value"]))
        }
      });
    if (G.common.async) {
      let w = new Map;
      return Promise.resolve().then(async () => {
        for (let B of W) {
          let A = await B.key,
            V = await B.value;
          if (A.status === "aborted" || V.status === "aborted") return w4;
          if (A.status === "dirty" || V.status === "dirty") d.dirty();
          w.set(A.value, V.value)
        }
        return {
          status: d.value,
          value: w
        }
      })
    } else {
      let w = new Map;
      for (let B of W) {
        let {
          key: A,
          value: V
        } = B;
        if (A.status === "aborted" || V.status === "aborted") return w4;
        if (A.status === "dirty" || V.status === "dirty") d.dirty();
        w.set(A.value, V.value)
      }
      return {
        status: d.value,
        value: w
      }
    }
  }
}
// @from(Start 6019020, End 6020677)
class hJ extends P4 {
  _parse(I) {
    let {
      status: d,
      ctx: G
    } = this._processInputParams(I);
    if (G.parsedType !== _2.set) return d2(G, {
      code: y0.invalid_type,
      expected: _2.set,
      received: G.parsedType
    }), w4;
    let Z = this._def;
    if (Z.minSize !== null) {
      if (G.data.size < Z.minSize.value) d2(G, {
        code: y0.too_small,
        minimum: Z.minSize.value,
        type: "set",
        inclusive: !0,
        exact: !1,
        message: Z.minSize.message
      }), d.dirty()
    }
    if (Z.maxSize !== null) {
      if (G.data.size > Z.maxSize.value) d2(G, {
        code: y0.too_big,
        maximum: Z.maxSize.value,
        type: "set",
        inclusive: !0,
        exact: !1,
        message: Z.maxSize.message
      }), d.dirty()
    }
    let C = this._def.valueType;

    function W(B) {
      let A = new Set;
      for (let V of B) {
        if (V.status === "aborted") return w4;
        if (V.status === "dirty") d.dirty();
        A.add(V.value)
      }
      return {
        status: d.value,
        value: A
      }
    }
    let w = [...G.data.values()].map((B, A) => C._parse(new XW(G, B, G.path, A)));
    if (G.common.async) return Promise.all(w).then((B) => W(B));
    else return W(w)
  }
  min(I, d) {
    return new hJ({
      ...this._def,
      minSize: {
        value: I,
        message: M2.toString(d)
      }
    })
  }
  max(I, d) {
    return new hJ({
      ...this._def,
      maxSize: {
        value: I,
        message: M2.toString(d)
      }
    })
  }
  size(I, d) {
    return this.min(I, d).max(I, d)
  }
  nonempty(I) {
    return this.min(1, I)
  }
}
// @from(Start 6020822, End 6023190)
class WR extends P4 {
  constructor() {
    super(...arguments);
    this.validate = this.implement
  }
  _parse(I) {
    let {
      ctx: d
    } = this._processInputParams(I);
    if (d.parsedType !== _2.function) return d2(d, {
      code: y0.invalid_type,
      expected: _2.function,
      received: d.parsedType
    }), w4;

    function G(w, B) {
      return Fa({
        data: w,
        path: d.path,
        errorMaps: [d.common.contextualErrorMap, d.schemaErrorMap, Ha(), wR].filter((A) => !!A),
        issueData: {
          code: y0.invalid_arguments,
          argumentsError: B
        }
      })
    }

    function Z(w, B) {
      return Fa({
        data: w,
        path: d.path,
        errorMaps: [d.common.contextualErrorMap, d.schemaErrorMap, Ha(), wR].filter((A) => !!A),
        issueData: {
          code: y0.invalid_return_type,
          returnTypeError: B
        }
      })
    }
    let C = {
        errorMap: d.common.contextualErrorMap
      },
      W = d.data;
    if (this._def.returns instanceof jJ) {
      let w = this;
      return DI(async function(...B) {
        let A = new AG([]),
          V = await w._def.args.parseAsync(B, C).catch((F) => {
            throw A.addIssue(G(B, F)), A
          }),
          X = await Reflect.apply(W, this, V);
        return await w._def.returns._def.type.parseAsync(X, C).catch((F) => {
          throw A.addIssue(Z(X, F)), A
        })
      })
    } else {
      let w = this;
      return DI(function(...B) {
        let A = w._def.args.safeParse(B, C);
        if (!A.success) throw new AG([G(B, A.error)]);
        let V = Reflect.apply(W, this, A.data),
          X = w._def.returns.safeParse(V, C);
        if (!X.success) throw new AG([Z(V, X.error)]);
        return X.data
      })
    }
  }
  parameters() {
    return this._def.args
  }
  returnType() {
    return this._def.returns
  }
  args(...I) {
    return new WR({
      ...this._def,
      args: MB.create(I).rest(eD.create())
    })
  }
  returns(I) {
    return new WR({
      ...this._def,
      returns: I
    })
  }
  implement(I) {
    return this.parse(I)
  }
  strictImplement(I) {
    return this.parse(I)
  }
  static create(I, d, G) {
    return new WR({
      args: I ? I : MB.create([]).rest(eD.create()),
      returns: d || eD.create(),
      typeName: T0.ZodFunction,
      ...S4(G)
    })
  }
}
// @from(Start 6023191, End 6023445)
class _R extends P4 {
  get schema() {
    return this._def.getter()
  }
  _parse(I) {
    let {
      ctx: d
    } = this._processInputParams(I);
    return this._def.getter()._parse({
      data: d.data,
      path: d.path,
      parent: d
    })
  }
}
// @from(Start 6023550, End 6023911)
class DR extends P4 {
  _parse(I) {
    if (I.data !== this._def.value) {
      let d = this._getOrReturnCtx(I);
      return d2(d, {
        received: d.data,
        code: y0.invalid_literal,
        expected: this._def.value
      }), w4
    }
    return {
      status: "valid",
      value: I.data
    }
  }
  get value() {
    return this._def.value
  }
}
// @from(Start 6024019, End 6024118)
function XW2(I, d) {
  return new dH({
    values: I,
    typeName: T0.ZodEnum,
    ...S4(d)
  })
}
// @from(Start 6024119, End 6025378)
class dH extends P4 {
  constructor() {
    super(...arguments);
    Y$.set(this, void 0)
  }
  _parse(I) {
    if (typeof I.data !== "string") {
      let d = this._getOrReturnCtx(I),
        G = this._def.values;
      return d2(d, {
        expected: N5.joinValues(G),
        received: d.parsedType,
        code: y0.invalid_type
      }), w4
    }
    if (!ga(this, Y$, "f")) wW2(this, Y$, new Set(this._def.values), "f");
    if (!ga(this, Y$, "f").has(I.data)) {
      let d = this._getOrReturnCtx(I),
        G = this._def.values;
      return d2(d, {
        received: d.data,
        code: y0.invalid_enum_value,
        options: G
      }), w4
    }
    return DI(I.data)
  }
  get options() {
    return this._def.values
  }
  get enum() {
    let I = {};
    for (let d of this._def.values) I[d] = d;
    return I
  }
  get Values() {
    let I = {};
    for (let d of this._def.values) I[d] = d;
    return I
  }
  get Enum() {
    let I = {};
    for (let d of this._def.values) I[d] = d;
    return I
  }
  extract(I, d = this._def) {
    return dH.create(I, {
      ...this._def,
      ...d
    })
  }
  exclude(I, d = this._def) {
    return dH.create(this.options.filter((G) => !I.includes(G)), {
      ...this._def,
      ...d
    })
  }
}
// @from(Start 6025414, End 6026220)
class HR extends P4 {
  constructor() {
    super(...arguments);
    _$.set(this, void 0)
  }
  _parse(I) {
    let d = N5.getValidEnumValues(this._def.values),
      G = this._getOrReturnCtx(I);
    if (G.parsedType !== _2.string && G.parsedType !== _2.number) {
      let Z = N5.objectValues(d);
      return d2(G, {
        expected: N5.joinValues(Z),
        received: G.parsedType,
        code: y0.invalid_type
      }), w4
    }
    if (!ga(this, _$, "f")) wW2(this, _$, new Set(N5.getValidEnumValues(this._def.values)), "f");
    if (!ga(this, _$, "f").has(I.data)) {
      let Z = N5.objectValues(d);
      return d2(G, {
        received: G.data,
        code: y0.invalid_enum_value,
        options: Z
      }), w4
    }
    return DI(I.data)
  }
  get enum() {
    return this._def.values
  }
}
// @from(Start 6026349, End 6026903)
class jJ extends P4 {
  unwrap() {
    return this._def.type
  }
  _parse(I) {
    let {
      ctx: d
    } = this._processInputParams(I);
    if (d.parsedType !== _2.promise && d.common.async === !1) return d2(d, {
      code: y0.invalid_type,
      expected: _2.promise,
      received: d.parsedType
    }), w4;
    let G = d.parsedType === _2.promise ? d.data : Promise.resolve(d.data);
    return DI(G.then((Z) => {
      return this._def.type.parseAsync(Z, {
        path: d.path,
        errorMap: d.common.contextualErrorMap
      })
    }))
  }
}
// @from(Start 6027009, End 6030253)
class bZ extends P4 {
  innerType() {
    return this._def.schema
  }
  sourceType() {
    return this._def.schema._def.typeName === T0.ZodEffects ? this._def.schema.sourceType() : this._def.schema
  }
  _parse(I) {
    let {
      status: d,
      ctx: G
    } = this._processInputParams(I), Z = this._def.effect || null, C = {
      addIssue: (W) => {
        if (d2(G, W), W.fatal) d.abort();
        else d.dirty()
      },
      get path() {
        return G.path
      }
    };
    if (C.addIssue = C.addIssue.bind(C), Z.type === "preprocess") {
      let W = Z.transform(G.data, C);
      if (G.common.async) return Promise.resolve(W).then(async (w) => {
        if (d.value === "aborted") return w4;
        let B = await this._def.schema._parseAsync({
          data: w,
          path: G.path,
          parent: G
        });
        if (B.status === "aborted") return w4;
        if (B.status === "dirty") return CR(B.value);
        if (d.value === "dirty") return CR(B.value);
        return B
      });
      else {
        if (d.value === "aborted") return w4;
        let w = this._def.schema._parseSync({
          data: W,
          path: G.path,
          parent: G
        });
        if (w.status === "aborted") return w4;
        if (w.status === "dirty") return CR(w.value);
        if (d.value === "dirty") return CR(w.value);
        return w
      }
    }
    if (Z.type === "refinement") {
      let W = (w) => {
        let B = Z.refinement(w, C);
        if (G.common.async) return Promise.resolve(B);
        if (B instanceof Promise) throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");
        return w
      };
      if (G.common.async === !1) {
        let w = this._def.schema._parseSync({
          data: G.data,
          path: G.path,
          parent: G
        });
        if (w.status === "aborted") return w4;
        if (w.status === "dirty") d.dirty();
        return W(w.value), {
          status: d.value,
          value: w.value
        }
      } else return this._def.schema._parseAsync({
        data: G.data,
        path: G.path,
        parent: G
      }).then((w) => {
        if (w.status === "aborted") return w4;
        if (w.status === "dirty") d.dirty();
        return W(w.value).then(() => {
          return {
            status: d.value,
            value: w.value
          }
        })
      })
    }
    if (Z.type === "transform")
      if (G.common.async === !1) {
        let W = this._def.schema._parseSync({
          data: G.data,
          path: G.path,
          parent: G
        });
        if (!mJ(W)) return W;
        let w = Z.transform(W.value, C);
        if (w instanceof Promise) throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");
        return {
          status: d.value,
          value: w
        }
      } else return this._def.schema._parseAsync({
        data: G.data,
        path: G.path,
        parent: G
      }).then((W) => {
        if (!mJ(W)) return W;
        return Promise.resolve(Z.transform(W.value, C)).then((w) => ({
          status: d.value,
          value: w
        }))
      });
    N5.assertNever(Z)
  }
}
// @from(Start 6030569, End 6030761)
class VG extends P4 {
  _parse(I) {
    if (this._getType(I) === _2.undefined) return DI(void 0);
    return this._def.innerType._parse(I)
  }
  unwrap() {
    return this._def.innerType
  }
}
// @from(Start 6030873, End 6031058)
class GX extends P4 {
  _parse(I) {
    if (this._getType(I) === _2.null) return DI(null);
    return this._def.innerType._parse(I)
  }
  unwrap() {
    return this._def.innerType
  }
}
// @from(Start 6031170, End 6031505)
class FR extends P4 {
  _parse(I) {
    let {
      ctx: d
    } = this._processInputParams(I), G = d.data;
    if (d.parsedType === _2.undefined) G = this._def.defaultValue();
    return this._def.innerType._parse({
      data: G,
      path: d.path,
      parent: d
    })
  }
  removeDefault() {
    return this._def.innerType
  }
}
// @from(Start 6031697, End 6032559)
class gR extends P4 {
  _parse(I) {
    let {
      ctx: d
    } = this._processInputParams(I), G = {
      ...d,
      common: {
        ...d.common,
        issues: []
      }
    }, Z = this._def.innerType._parse({
      data: G.data,
      path: G.path,
      parent: {
        ...G
      }
    });
    if (D$(Z)) return Z.then((C) => {
      return {
        status: "valid",
        value: C.status === "valid" ? C.value : this._def.catchValue({
          get error() {
            return new AG(G.common.issues)
          },
          input: G.data
        })
      }
    });
    else return {
      status: "valid",
      value: Z.status === "valid" ? Z.value : this._def.catchValue({
        get error() {
          return new AG(G.common.issues)
        },
        input: G.data
      })
    }
  }
  removeCatch() {
    return this._def.innerType
  }
}
// @from(Start 6032741, End 6033050)
class K$ extends P4 {
  _parse(I) {
    if (this._getType(I) !== _2.nan) {
      let G = this._getOrReturnCtx(I);
      return d2(G, {
        code: y0.invalid_type,
        expected: _2.nan,
        received: G.parsedType
      }), w4
    }
    return {
      status: "valid",
      value: I.data
    }
  }
}
// @from(Start 6033140, End 6033165)
Ps5 = Symbol("zod_brand")
// @from(Start 6033167, End 6033416)
class Ka extends P4 {
  _parse(I) {
    let {
      ctx: d
    } = this._processInputParams(I), G = d.data;
    return this._def.type._parse({
      data: G,
      path: d.path,
      parent: d
    })
  }
  unwrap() {
    return this._def.type
  }
}
// @from(Start 6033417, End 6034449)
class N$ extends P4 {
  _parse(I) {
    let {
      status: d,
      ctx: G
    } = this._processInputParams(I);
    if (G.common.async) return (async () => {
      let C = await this._def.in._parseAsync({
        data: G.data,
        path: G.path,
        parent: G
      });
      if (C.status === "aborted") return w4;
      if (C.status === "dirty") return d.dirty(), CR(C.value);
      else return this._def.out._parseAsync({
        data: C.value,
        path: G.path,
        parent: G
      })
    })();
    else {
      let Z = this._def.in._parseSync({
        data: G.data,
        path: G.path,
        parent: G
      });
      if (Z.status === "aborted") return w4;
      if (Z.status === "dirty") return d.dirty(), {
        status: "dirty",
        value: Z.value
      };
      else return this._def.out._parseSync({
        data: Z.value,
        path: G.path,
        parent: G
      })
    }
  }
  static create(I, d) {
    return new N$({
      in: I,
      out: d,
      typeName: T0.ZodPipeline
    })
  }
}