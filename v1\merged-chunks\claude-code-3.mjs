
// @from(Start 6034450, End 6034726)
class JR extends P4 {
  _parse(I) {
    let d = this._def.innerType._parse(I),
      G = (Z) => {
        if (mJ(Z)) Z.value = Object.freeze(Z.value);
        return Z
      };
    return D$(d) ? d.then((Z) => G(Z)) : G(d)
  }
  unwrap() {
    return this._def.innerType
  }
}
// @from(Start 6034839, End 6035351)
function YW2(I, d = {}, G) {
  if (I) return bJ.create().superRefine((Z, C) => {
    var W, w;
    if (!I(Z)) {
      let B = typeof d === "function" ? d(Z) : typeof d === "string" ? {
          message: d
        } : d,
        A = (w = (W = B.fatal) !== null && W !== void 0 ? W : G) !== null && w !== void 0 ? w : !0,
        V = typeof B === "string" ? {
          message: B
        } : B;
      C.addIssue({
        code: "custom",
        ...V,
        fatal: A
      })
    }
  });
  return bJ.create()
}
// @from(Start 6035356, End 6035393)
$s5 = {
    object: M3.lazycreate
  }
// @from(Start 6035397, End 6035399)
T0
// @from(Start 6036444, End 6036544)
us5 = (I, d = {
    message: `Input not instance of ${I.name}`
  }) => YW2((G) => G instanceof I, d)
// @from(Start 6036548, End 6036563)
_W2 = AW.create
// @from(Start 6036567, End 6036582)
DW2 = tD.create
// @from(Start 6036586, End 6036601)
Ts5 = K$.create
// @from(Start 6036605, End 6036620)
Os5 = IH.create
// @from(Start 6036624, End 6036639)
HW2 = BR.create
// @from(Start 6036643, End 6036658)
ms5 = lJ.create
// @from(Start 6036662, End 6036677)
ls5 = H$.create
// @from(Start 6036681, End 6036696)
bs5 = AR.create
// @from(Start 6036700, End 6036715)
hs5 = VR.create
// @from(Start 6036719, End 6036734)
js5 = bJ.create
// @from(Start 6036738, End 6036753)
ks5 = eD.create
// @from(Start 6036757, End 6036772)
xs5 = EB.create
// @from(Start 6036776, End 6036791)
cs5 = F$.create
// @from(Start 6036795, End 6036810)
ps5 = VW.create
// @from(Start 6036814, End 6036829)
is5 = M3.create
// @from(Start 6036833, End 6036854)
ns5 = M3.strictCreate
// @from(Start 6036858, End 6036873)
rs5 = XR.create
// @from(Start 6036877, End 6036892)
as5 = Ja.create
// @from(Start 6036896, End 6036911)
ss5 = YR.create
// @from(Start 6036915, End 6036930)
os5 = MB.create
// @from(Start 6036934, End 6036949)
es5 = g$.create
// @from(Start 6036953, End 6036968)
ts5 = J$.create
// @from(Start 6036972, End 6036987)
Io5 = hJ.create
// @from(Start 6036991, End 6037006)
do5 = WR.create
// @from(Start 6037010, End 6037025)
Go5 = _R.create
// @from(Start 6037029, End 6037044)
Zo5 = DR.create
// @from(Start 6037048, End 6037063)
Co5 = dH.create
// @from(Start 6037067, End 6037082)
Wo5 = HR.create
// @from(Start 6037086, End 6037101)
wo5 = jJ.create
// @from(Start 6037105, End 6037120)
CW2 = bZ.create
// @from(Start 6037124, End 6037139)
Bo5 = VG.create
// @from(Start 6037143, End 6037158)
Ao5 = GX.create
// @from(Start 6037162, End 6037191)
Vo5 = bZ.createWithPreprocess
// @from(Start 6037195, End 6037210)
Xo5 = N$.create
// @from(Start 6037214, End 6037242)
Yo5 = () => _W2().optional()
// @from(Start 6037246, End 6037274)
_o5 = () => DW2().optional()
// @from(Start 6037278, End 6037306)
Do5 = () => HW2().optional()
// @from(Start 6037310, End 6037659)
Ho5 = {
    string: (I) => AW.create({
      ...I,
      coerce: !0
    }),
    number: (I) => tD.create({
      ...I,
      coerce: !0
    }),
    boolean: (I) => BR.create({
      ...I,
      coerce: !0
    }),
    bigint: (I) => IH.create({
      ...I,
      coerce: !0
    }),
    date: (I) => lJ.create({
      ...I,
      coerce: !0
    })
  }
// @from(Start 6037663, End 6037671)
Fo5 = w4
// @from(Start 6037675, End 6039822)
s = Object.freeze({
    __proto__: null,
    defaultErrorMap: wR,
    setErrorMap: Vs5,
    getErrorMap: Ha,
    makeIssue: Fa,
    EMPTY_PATH: Xs5,
    addIssueToContext: d2,
    ParseStatus: h7,
    INVALID: w4,
    DIRTY: CR,
    OK: DI,
    isAborted: zF1,
    isDirty: QF1,
    isValid: mJ,
    isAsync: D$,
    get util() {
      return N5
    },
    get objectUtil() {
      return NF1
    },
    ZodParsedType: _2,
    getParsedType: dX,
    ZodType: P4,
    datetimeRegex: VW2,
    ZodString: AW,
    ZodNumber: tD,
    ZodBigInt: IH,
    ZodBoolean: BR,
    ZodDate: lJ,
    ZodSymbol: H$,
    ZodUndefined: AR,
    ZodNull: VR,
    ZodAny: bJ,
    ZodUnknown: eD,
    ZodNever: EB,
    ZodVoid: F$,
    ZodArray: VW,
    ZodObject: M3,
    ZodUnion: XR,
    ZodDiscriminatedUnion: Ja,
    ZodIntersection: YR,
    ZodTuple: MB,
    ZodRecord: g$,
    ZodMap: J$,
    ZodSet: hJ,
    ZodFunction: WR,
    ZodLazy: _R,
    ZodLiteral: DR,
    ZodEnum: dH,
    ZodNativeEnum: HR,
    ZodPromise: jJ,
    ZodEffects: bZ,
    ZodTransformer: bZ,
    ZodOptional: VG,
    ZodNullable: GX,
    ZodDefault: FR,
    ZodCatch: gR,
    ZodNaN: K$,
    BRAND: Ps5,
    ZodBranded: Ka,
    ZodPipeline: N$,
    ZodReadonly: JR,
    custom: YW2,
    Schema: P4,
    ZodSchema: P4,
    late: $s5,
    get ZodFirstPartyTypeKind() {
      return T0
    },
    coerce: Ho5,
    any: js5,
    array: ps5,
    bigint: Os5,
    boolean: HW2,
    date: ms5,
    discriminatedUnion: as5,
    effect: CW2,
    enum: Co5,
    function: do5,
    instanceof: us5,
    intersection: ss5,
    lazy: Go5,
    literal: Zo5,
    map: ts5,
    nan: Ts5,
    nativeEnum: Wo5,
    never: xs5,
    null: hs5,
    nullable: Ao5,
    number: DW2,
    object: is5,
    oboolean: Do5,
    onumber: _o5,
    optional: Bo5,
    ostring: Yo5,
    pipeline: Xo5,
    preprocess: Vo5,
    promise: wo5,
    record: es5,
    set: Io5,
    strictObject: ns5,
    string: _W2,
    symbol: ls5,
    transformer: CW2,
    tuple: os5,
    undefined: bs5,
    union: rs5,
    unknown: ks5,
    void: cs5,
    NEVER: Fo5,
    ZodIssueCode: y0,
    quotelessJson: As5,
    ZodError: AG
  })
// @from(Start 6039825, End 6039855)
function FW2() {
  return {}
}
// @from(Start 6039857, End 6040389)
function gW2(I, d) {
  let G = {
    type: "array"
  };
  if (I.type?._def && I.type?._def?.typeName !== T0.ZodAny) G.items = o2(I.type._def, {
    ...d,
    currentPath: [...d.currentPath, "items"]
  });
  if (I.minLength) X5(G, "minItems", I.minLength.value, I.minLength.message, d);
  if (I.maxLength) X5(G, "maxItems", I.maxLength.value, I.maxLength.message, d);
  if (I.exactLength) X5(G, "minItems", I.exactLength.value, I.exactLength.message, d), X5(G, "maxItems", I.exactLength.value, I.exactLength.message, d);
  return G
}
// @from(Start 6040391, End 6041274)
function JW2(I, d) {
  let G = {
    type: "integer",
    format: "int64"
  };
  if (!I.checks) return G;
  for (let Z of I.checks) switch (Z.kind) {
    case "min":
      if (d.target === "jsonSchema7")
        if (Z.inclusive) X5(G, "minimum", Z.value, Z.message, d);
        else X5(G, "exclusiveMinimum", Z.value, Z.message, d);
      else {
        if (!Z.inclusive) G.exclusiveMinimum = !0;
        X5(G, "minimum", Z.value, Z.message, d)
      }
      break;
    case "max":
      if (d.target === "jsonSchema7")
        if (Z.inclusive) X5(G, "maximum", Z.value, Z.message, d);
        else X5(G, "exclusiveMaximum", Z.value, Z.message, d);
      else {
        if (!Z.inclusive) G.exclusiveMaximum = !0;
        X5(G, "maximum", Z.value, Z.message, d)
      }
      break;
    case "multipleOf":
      X5(G, "multipleOf", Z.value, Z.message, d);
      break
  }
  return G
}
// @from(Start 6041276, End 6041329)
function KW2() {
  return {
    type: "boolean"
  }
}
// @from(Start 6041331, End 6041380)
function Na(I, d) {
  return o2(I.type._def, d)
}
// @from(Start 6041385, End 6041437)
NW2 = (I, d) => {
  return o2(I.innerType._def, d)
}
// @from(Start 6041440, End 6041840)
function qF1(I, d, G) {
  let Z = G ?? d.dateStrategy;
  if (Array.isArray(Z)) return {
    anyOf: Z.map((C, W) => qF1(I, d, C))
  };
  switch (Z) {
    case "string":
    case "format:date-time":
      return {
        type: "string", format: "date-time"
      };
    case "format:date":
      return {
        type: "string", format: "date"
      };
    case "integer":
      return go5(I, d)
  }
}
// @from(Start 6041845, End 6042177)
go5 = (I, d) => {
  let G = {
    type: "integer",
    format: "unix-time"
  };
  if (d.target === "openApi3") return G;
  for (let Z of I.checks) switch (Z.kind) {
    case "min":
      X5(G, "minimum", Z.value, Z.message, d);
      break;
    case "max":
      X5(G, "maximum", Z.value, Z.message, d);
      break
  }
  return G
}
// @from(Start 6042180, End 6042279)
function zW2(I, d) {
  return {
    ...o2(I.innerType._def, d),
    default: I.defaultValue()
  }
}
// @from(Start 6042281, End 6042369)
function QW2(I, d) {
  return d.effectStrategy === "input" ? o2(I.schema._def, d) : {}
}
// @from(Start 6042371, End 6042456)
function fW2(I) {
  return {
    type: "string",
    enum: Array.from(I.values)
  }
}
// @from(Start 6042461, End 6042552)
Jo5 = (I) => {
  if ("type" in I && I.type === "string") return !1;
  return "allOf" in I
}
// @from(Start 6042555, End 6043316)
function qW2(I, d) {
  let G = [o2(I.left._def, {
      ...d,
      currentPath: [...d.currentPath, "allOf", "0"]
    }), o2(I.right._def, {
      ...d,
      currentPath: [...d.currentPath, "allOf", "1"]
    })].filter((W) => !!W),
    Z = d.target === "jsonSchema2019-09" ? {
      unevaluatedProperties: !1
    } : void 0,
    C = [];
  return G.forEach((W) => {
    if (Jo5(W)) {
      if (C.push(...W.allOf), W.unevaluatedProperties === void 0) Z = void 0
    } else {
      let w = W;
      if ("additionalProperties" in W && W.additionalProperties === !1) {
        let {
          additionalProperties: B,
          ...A
        } = W;
        w = A
      } else Z = void 0;
      C.push(w)
    }
  }), C.length ? {
    allOf: C,
    ...Z
  } : void 0
}
// @from(Start 6043318, End 6043694)
function RW2(I, d) {
  let G = typeof I.value;
  if (G !== "bigint" && G !== "number" && G !== "boolean" && G !== "string") return {
    type: Array.isArray(I.value) ? "array" : "object"
  };
  if (d.target === "openApi3") return {
    type: G === "bigint" ? "integer" : G,
    enum: [I.value]
  };
  return {
    type: G === "bigint" ? "integer" : G,
    const: I.value
  }
}
// @from(Start 6043699, End 6043711)
RF1 = void 0
// @from(Start 6043715, End 6045827)
YW = {
    cuid: /^[cC][^\s-]{8,}$/,
    cuid2: /^[0-9a-z]+$/,
    ulid: /^[0-9A-HJKMNP-TV-Z]{26}$/,
    email: /^(?!\.)(?!.*\.\.)([a-zA-Z0-9_'+\-\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\-]*\.)+[a-zA-Z]{2,}$/,
    emoji: () => {
      if (RF1 === void 0) RF1 = RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$", "u");
      return RF1
    },
    uuid: /^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/,
    ipv4: /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,
    ipv4Cidr: /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,
    ipv6: /^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/,
    ipv6Cidr: /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,
    base64: /^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,
    base64url: /^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,
    nanoid: /^[a-zA-Z0-9_-]{21}$/,
    jwt: /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/
  }
// @from(Start 6045830, End 6049123)
function za(I, d) {
  let G = {
    type: "string"
  };
  if (I.checks)
    for (let Z of I.checks) switch (Z.kind) {
      case "min":
        X5(G, "minLength", typeof G.minLength === "number" ? Math.max(G.minLength, Z.value) : Z.value, Z.message, d);
        break;
      case "max":
        X5(G, "maxLength", typeof G.maxLength === "number" ? Math.min(G.maxLength, Z.value) : Z.value, Z.message, d);
        break;
      case "email":
        switch (d.emailStrategy) {
          case "format:email":
            _W(G, "email", Z.message, d);
            break;
          case "format:idn-email":
            _W(G, "idn-email", Z.message, d);
            break;
          case "pattern:zod":
            HI(G, YW.email, Z.message, d);
            break
        }
        break;
      case "url":
        _W(G, "uri", Z.message, d);
        break;
      case "uuid":
        _W(G, "uuid", Z.message, d);
        break;
      case "regex":
        HI(G, Z.regex, Z.message, d);
        break;
      case "cuid":
        HI(G, YW.cuid, Z.message, d);
        break;
      case "cuid2":
        HI(G, YW.cuid2, Z.message, d);
        break;
      case "startsWith":
        HI(G, RegExp(`^${UF1(Z.value,d)}`), Z.message, d);
        break;
      case "endsWith":
        HI(G, RegExp(`${UF1(Z.value,d)}$`), Z.message, d);
        break;
      case "datetime":
        _W(G, "date-time", Z.message, d);
        break;
      case "date":
        _W(G, "date", Z.message, d);
        break;
      case "time":
        _W(G, "time", Z.message, d);
        break;
      case "duration":
        _W(G, "duration", Z.message, d);
        break;
      case "length":
        X5(G, "minLength", typeof G.minLength === "number" ? Math.max(G.minLength, Z.value) : Z.value, Z.message, d), X5(G, "maxLength", typeof G.maxLength === "number" ? Math.min(G.maxLength, Z.value) : Z.value, Z.message, d);
        break;
      case "includes": {
        HI(G, RegExp(UF1(Z.value, d)), Z.message, d);
        break
      }
      case "ip": {
        if (Z.version !== "v6") _W(G, "ipv4", Z.message, d);
        if (Z.version !== "v4") _W(G, "ipv6", Z.message, d);
        break
      }
      case "base64url":
        HI(G, YW.base64url, Z.message, d);
        break;
      case "jwt":
        HI(G, YW.jwt, Z.message, d);
        break;
      case "cidr": {
        if (Z.version !== "v6") HI(G, YW.ipv4Cidr, Z.message, d);
        if (Z.version !== "v4") HI(G, YW.ipv6Cidr, Z.message, d);
        break
      }
      case "emoji":
        HI(G, YW.emoji(), Z.message, d);
        break;
      case "ulid": {
        HI(G, YW.ulid, Z.message, d);
        break
      }
      case "base64": {
        switch (d.base64Strategy) {
          case "format:binary": {
            _W(G, "binary", Z.message, d);
            break
          }
          case "contentEncoding:base64": {
            X5(G, "contentEncoding", "base64", Z.message, d);
            break
          }
          case "pattern:zod": {
            HI(G, YW.base64, Z.message, d);
            break
          }
        }
        break
      }
      case "nanoid":
        HI(G, YW.nanoid, Z.message, d);
      case "toLowerCase":
      case "toUpperCase":
      case "trim":
        break;
      default:
        ((C) => {})(Z)
    }
  return G
}
// @from(Start 6049125, End 6049200)
function UF1(I, d) {
  return d.patternStrategy === "escape" ? No5(I) : I
}
// @from(Start 6049205, End 6049282)
Ko5 = new Set("ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789")
// @from(Start 6049285, End 6049421)
function No5(I) {
  let d = "";
  for (let G = 0; G < I.length; G++) {
    if (!Ko5.has(I[G])) d += "\\";
    d += I[G]
  }
  return d
}
// @from(Start 6049423, End 6050098)
function _W(I, d, G, Z) {
  if (I.format || I.anyOf?.some((C) => C.format)) {
    if (!I.anyOf) I.anyOf = [];
    if (I.format) {
      if (I.anyOf.push({
          format: I.format,
          ...I.errorMessage && Z.errorMessages && {
            errorMessage: {
              format: I.errorMessage.format
            }
          }
        }), delete I.format, I.errorMessage) {
        if (delete I.errorMessage.format, Object.keys(I.errorMessage).length === 0) delete I.errorMessage
      }
    }
    I.anyOf.push({
      format: d,
      ...G && Z.errorMessages && {
        errorMessage: {
          format: G
        }
      }
    })
  } else X5(I, "format", d, G, Z)
}
// @from(Start 6050100, End 6050803)
function HI(I, d, G, Z) {
  if (I.pattern || I.allOf?.some((C) => C.pattern)) {
    if (!I.allOf) I.allOf = [];
    if (I.pattern) {
      if (I.allOf.push({
          pattern: I.pattern,
          ...I.errorMessage && Z.errorMessages && {
            errorMessage: {
              pattern: I.errorMessage.pattern
            }
          }
        }), delete I.pattern, I.errorMessage) {
        if (delete I.errorMessage.pattern, Object.keys(I.errorMessage).length === 0) delete I.errorMessage
      }
    }
    I.allOf.push({
      pattern: UW2(d, Z),
      ...G && Z.errorMessages && {
        errorMessage: {
          pattern: G
        }
      }
    })
  } else X5(I, "pattern", UW2(d, Z), G, Z)
}
// @from(Start 6050805, End 6052243)
function UW2(I, d) {
  if (!d.applyRegexFlags || !I.flags) return I.source;
  let G = {
      i: I.flags.includes("i"),
      m: I.flags.includes("m"),
      s: I.flags.includes("s")
    },
    Z = G.i ? I.source.toLowerCase() : I.source,
    C = "",
    W = !1,
    w = !1,
    B = !1;
  for (let A = 0; A < Z.length; A++) {
    if (W) {
      C += Z[A], W = !1;
      continue
    }
    if (G.i) {
      if (w) {
        if (Z[A].match(/[a-z]/)) {
          if (B) C += Z[A], C += `${Z[A-2]}-${Z[A]}`.toUpperCase(), B = !1;
          else if (Z[A + 1] === "-" && Z[A + 2]?.match(/[a-z]/)) C += Z[A], B = !0;
          else C += `${Z[A]}${Z[A].toUpperCase()}`;
          continue
        }
      } else if (Z[A].match(/[a-z]/)) {
        C += `[${Z[A]}${Z[A].toUpperCase()}]`;
        continue
      }
    }
    if (G.m) {
      if (Z[A] === "^") {
        C += `(^|(?<=[\r
]))`;
        continue
      } else if (Z[A] === "$") {
        C += `($|(?=[\r
]))`;
        continue
      }
    }
    if (G.s && Z[A] === ".") {
      C += w ? `${Z[A]}\r
` : `[${Z[A]}\r
]`;
      continue
    }
    if (C += Z[A], Z[A] === "\\") W = !0;
    else if (w && Z[A] === "]") w = !1;
    else if (!w && Z[A] === "[") w = !0
  }
  try {
    new RegExp(C)
  } catch {
    return console.warn(`Could not convert regex pattern at ${d.currentPath.join("/")} to a flag-independent form! Falling back to the flag-ignorant source`), I.source
  }
  return C
}
// @from(Start 6052245, End 6053630)
function Qa(I, d) {
  if (d.target === "openAi") console.warn("Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead.");
  if (d.target === "openApi3" && I.keyType?._def.typeName === T0.ZodEnum) return {
    type: "object",
    required: I.keyType._def.values,
    properties: I.keyType._def.values.reduce((Z, C) => ({
      ...Z,
      [C]: o2(I.valueType._def, {
        ...d,
        currentPath: [...d.currentPath, "properties", C]
      }) ?? {}
    }), {}),
    additionalProperties: !1
  };
  let G = {
    type: "object",
    additionalProperties: o2(I.valueType._def, {
      ...d,
      currentPath: [...d.currentPath, "additionalProperties"]
    }) ?? {}
  };
  if (d.target === "openApi3") return G;
  if (I.keyType?._def.typeName === T0.ZodString && I.keyType._def.checks?.length) {
    let {
      type: Z,
      ...C
    } = za(I.keyType._def, d);
    return {
      ...G,
      propertyNames: C
    }
  } else if (I.keyType?._def.typeName === T0.ZodEnum) return {
    ...G,
    propertyNames: {
      enum: I.keyType._def.values
    }
  };
  else if (I.keyType?._def.typeName === T0.ZodBranded && I.keyType._def.type._def.typeName === T0.ZodString && I.keyType._def.type._def.checks?.length) {
    let {
      type: Z,
      ...C
    } = Na(I.keyType._def, d);
    return {
      ...G,
      propertyNames: C
    }
  }
  return G
}
// @from(Start 6053632, End 6054092)
function vW2(I, d) {
  if (d.mapStrategy === "record") return Qa(I, d);
  let G = o2(I.keyType._def, {
      ...d,
      currentPath: [...d.currentPath, "items", "items", "0"]
    }) || {},
    Z = o2(I.valueType._def, {
      ...d,
      currentPath: [...d.currentPath, "items", "items", "1"]
    }) || {};
  return {
    type: "array",
    maxItems: 125,
    items: {
      type: "array",
      items: [G, Z],
      minItems: 2,
      maxItems: 2
    }
  }
}
// @from(Start 6054094, End 6054416)
function EW2(I) {
  let d = I.values,
    Z = Object.keys(I.values).filter((W) => {
      return typeof d[d[W]] !== "number"
    }).map((W) => d[W]),
    C = Array.from(new Set(Z.map((W) => typeof W)));
  return {
    type: C.length === 1 ? C[0] === "string" ? "string" : "number" : ["string", "number"],
    enum: Z
  }
}
// @from(Start 6054418, End 6054463)
function MW2() {
  return {
    not: {}
  }
}
// @from(Start 6054465, End 6054587)
function SW2(I) {
  return I.target === "openApi3" ? {
    enum: ["null"],
    nullable: !0
  } : {
    type: "null"
  }
}
// @from(Start 6054592, End 6054713)
z$ = {
  ZodString: "string",
  ZodNumber: "number",
  ZodBigInt: "integer",
  ZodBoolean: "boolean",
  ZodNull: "null"
}
// @from(Start 6054716, End 6056165)
function yW2(I, d) {
  if (d.target === "openApi3") return LW2(I, d);
  let G = I.options instanceof Map ? Array.from(I.options.values()) : I.options;
  if (G.every((Z) => (Z._def.typeName in z$) && (!Z._def.checks || !Z._def.checks.length))) {
    let Z = G.reduce((C, W) => {
      let w = z$[W._def.typeName];
      return w && !C.includes(w) ? [...C, w] : C
    }, []);
    return {
      type: Z.length > 1 ? Z : Z[0]
    }
  } else if (G.every((Z) => Z._def.typeName === "ZodLiteral" && !Z.description)) {
    let Z = G.reduce((C, W) => {
      let w = typeof W._def.value;
      switch (w) {
        case "string":
        case "number":
        case "boolean":
          return [...C, w];
        case "bigint":
          return [...C, "integer"];
        case "object":
          if (W._def.value === null) return [...C, "null"];
        case "symbol":
        case "undefined":
        case "function":
        default:
          return C
      }
    }, []);
    if (Z.length === G.length) {
      let C = Z.filter((W, w, B) => B.indexOf(W) === w);
      return {
        type: C.length > 1 ? C : C[0],
        enum: G.reduce((W, w) => {
          return W.includes(w._def.value) ? W : [...W, w._def.value]
        }, [])
      }
    }
  } else if (G.every((Z) => Z._def.typeName === "ZodEnum")) return {
    type: "string",
    enum: G.reduce((Z, C) => [...Z, ...C._def.values.filter((W) => !Z.includes(W))], [])
  };
  return LW2(I, d)
}
// @from(Start 6056170, End 6056510)
LW2 = (I, d) => {
  let G = (I.options instanceof Map ? Array.from(I.options.values()) : I.options).map((Z, C) => o2(Z._def, {
    ...d,
    currentPath: [...d.currentPath, "anyOf", `${C}`]
  })).filter((Z) => !!Z && (!d.strictUnions || typeof Z === "object" && Object.keys(Z).length > 0));
  return G.length ? {
    anyOf: G
  } : void 0
}
// @from(Start 6056513, End 6057322)
function PW2(I, d) {
  if (["ZodString", "ZodNumber", "ZodBigInt", "ZodBoolean", "ZodNull"].includes(I.innerType._def.typeName) && (!I.innerType._def.checks || !I.innerType._def.checks.length)) {
    if (d.target === "openApi3") return {
      type: z$[I.innerType._def.typeName],
      nullable: !0
    };
    return {
      type: [z$[I.innerType._def.typeName], "null"]
    }
  }
  if (d.target === "openApi3") {
    let Z = o2(I.innerType._def, {
      ...d,
      currentPath: [...d.currentPath]
    });
    if (Z && "$ref" in Z) return {
      allOf: [Z],
      nullable: !0
    };
    return Z && {
      ...Z,
      nullable: !0
    }
  }
  let G = o2(I.innerType._def, {
    ...d,
    currentPath: [...d.currentPath, "anyOf", "0"]
  });
  return G && {
    anyOf: [G, {
      type: "null"
    }]
  }
}
// @from(Start 6057324, End 6058270)
function $W2(I, d) {
  let G = {
    type: "number"
  };
  if (!I.checks) return G;
  for (let Z of I.checks) switch (Z.kind) {
    case "int":
      G.type = "integer", JF1(G, "type", Z.message, d);
      break;
    case "min":
      if (d.target === "jsonSchema7")
        if (Z.inclusive) X5(G, "minimum", Z.value, Z.message, d);
        else X5(G, "exclusiveMinimum", Z.value, Z.message, d);
      else {
        if (!Z.inclusive) G.exclusiveMinimum = !0;
        X5(G, "minimum", Z.value, Z.message, d)
      }
      break;
    case "max":
      if (d.target === "jsonSchema7")
        if (Z.inclusive) X5(G, "maximum", Z.value, Z.message, d);
        else X5(G, "exclusiveMaximum", Z.value, Z.message, d);
      else {
        if (!Z.inclusive) G.exclusiveMaximum = !0;
        X5(G, "maximum", Z.value, Z.message, d)
      }
      break;
    case "multipleOf":
      X5(G, "multipleOf", Z.value, Z.message, d);
      break
  }
  return G
}
// @from(Start 6058272, End 6058716)
function zo5(I, d) {
  if (d.removeAdditionalStrategy === "strict") return I.catchall._def.typeName === "ZodNever" ? I.unknownKeys !== "strict" : o2(I.catchall._def, {
    ...d,
    currentPath: [...d.currentPath, "additionalProperties"]
  }) ?? !0;
  else return I.catchall._def.typeName === "ZodNever" ? I.unknownKeys === "passthrough" : o2(I.catchall._def, {
    ...d,
    currentPath: [...d.currentPath, "additionalProperties"]
  }) ?? !0
}
// @from(Start 6058718, End 6059652)
function uW2(I, d) {
  let G = d.target === "openAi",
    Z = {
      type: "object",
      ...Object.entries(I.shape()).reduce((C, [W, w]) => {
        if (w === void 0 || w._def === void 0) return C;
        let B = w.isOptional();
        if (B && G) {
          if (w instanceof VG) w = w._def.innerType;
          if (!w.isNullable()) w = w.nullable();
          B = !1
        }
        let A = o2(w._def, {
          ...d,
          currentPath: [...d.currentPath, "properties", W],
          propertyPath: [...d.currentPath, "properties", W]
        });
        if (A === void 0) return C;
        return {
          properties: {
            ...C.properties,
            [W]: A
          },
          required: B ? C.required : [...C.required, W]
        }
      }, {
        properties: {},
        required: []
      }),
      additionalProperties: zo5(I, d)
    };
  if (!Z.required.length) delete Z.required;
  return Z
}
// @from(Start 6059657, End 6059932)
TW2 = (I, d) => {
  if (d.currentPath.toString() === d.propertyPath?.toString()) return o2(I.innerType._def, d);
  let G = o2(I.innerType._def, {
    ...d,
    currentPath: [...d.currentPath, "anyOf", "1"]
  });
  return G ? {
    anyOf: [{
      not: {}
    }, G]
  } : {}
}
// @from(Start 6059938, End 6060348)
OW2 = (I, d) => {
  if (d.pipeStrategy === "input") return o2(I.in._def, d);
  else if (d.pipeStrategy === "output") return o2(I.out._def, d);
  let G = o2(I.in._def, {
      ...d,
      currentPath: [...d.currentPath, "allOf", "0"]
    }),
    Z = o2(I.out._def, {
      ...d,
      currentPath: [...d.currentPath, "allOf", G ? "1" : "0"]
    });
  return {
    allOf: [G, Z].filter((C) => C !== void 0)
  }
}
// @from(Start 6060351, End 6060401)
function mW2(I, d) {
  return o2(I.type._def, d)
}
// @from(Start 6060403, End 6060743)
function lW2(I, d) {
  let Z = {
    type: "array",
    uniqueItems: !0,
    items: o2(I.valueType._def, {
      ...d,
      currentPath: [...d.currentPath, "items"]
    })
  };
  if (I.minSize) X5(Z, "minItems", I.minSize.value, I.minSize.message, d);
  if (I.maxSize) X5(Z, "maxItems", I.maxSize.value, I.maxSize.message, d);
  return Z
}
// @from(Start 6060745, End 6061403)
function bW2(I, d) {
  if (I.rest) return {
    type: "array",
    minItems: I.items.length,
    items: I.items.map((G, Z) => o2(G._def, {
      ...d,
      currentPath: [...d.currentPath, "items", `${Z}`]
    })).reduce((G, Z) => Z === void 0 ? G : [...G, Z], []),
    additionalItems: o2(I.rest._def, {
      ...d,
      currentPath: [...d.currentPath, "additionalItems"]
    })
  };
  else return {
    type: "array",
    minItems: I.items.length,
    maxItems: I.items.length,
    items: I.items.map((G, Z) => o2(G._def, {
      ...d,
      currentPath: [...d.currentPath, "items", `${Z}`]
    })).reduce((G, Z) => Z === void 0 ? G : [...G, Z], [])
  }
}
// @from(Start 6061405, End 6061450)
function hW2() {
  return {
    not: {}
  }
}
// @from(Start 6061452, End 6061482)
function jW2() {
  return {}
}
// @from(Start 6061487, End 6061539)
kW2 = (I, d) => {
  return o2(I.innerType._def, d)
}
// @from(Start 6061542, End 6061943)
function o2(I, d, G = !1) {
  let Z = d.seen.get(I);
  if (d.override) {
    let w = d.override?.(I, d, Z, G);
    if (w !== IW2) return w
  }
  if (Z && !G) {
    let w = Qo5(Z, d);
    if (w !== void 0) return w
  }
  let C = {
    def: I,
    path: d.currentPath,
    jsonSchema: void 0
  };
  d.seen.set(I, C);
  let W = qo5(I, I.typeName, d);
  if (W) Ro5(I, d, W);
  return C.jsonSchema = W, W
}
// @from(Start 6061948, End 6062488)
Qo5 = (I, d) => {
    switch (d.$refStrategy) {
      case "root":
        return {
          $ref: I.path.join("/")
        };
      case "relative":
        return {
          $ref: fo5(d.currentPath, I.path)
        };
      case "none":
      case "seen": {
        if (I.path.length < d.currentPath.length && I.path.every((G, Z) => d.currentPath[Z] === G)) return console.warn(`Recursive reference detected at ${d.currentPath.join("/")}! Defaulting to any`), {};
        return d.$refStrategy === "seen" ? {} : void 0
      }
    }
  }
// @from(Start 6062492, End 6062670)
fo5 = (I, d) => {
    let G = 0;
    for (; G < I.length && G < d.length; G++)
      if (I[G] !== d[G]) break;
    return [(I.length - G).toString(), ...d.slice(G)].join("/")
  }
// @from(Start 6062674, End 6064510)
qo5 = (I, d, G) => {
    switch (d) {
      case T0.ZodString:
        return za(I, G);
      case T0.ZodNumber:
        return $W2(I, G);
      case T0.ZodObject:
        return uW2(I, G);
      case T0.ZodBigInt:
        return JW2(I, G);
      case T0.ZodBoolean:
        return KW2();
      case T0.ZodDate:
        return qF1(I, G);
      case T0.ZodUndefined:
        return hW2();
      case T0.ZodNull:
        return SW2(G);
      case T0.ZodArray:
        return gW2(I, G);
      case T0.ZodUnion:
      case T0.ZodDiscriminatedUnion:
        return yW2(I, G);
      case T0.ZodIntersection:
        return qW2(I, G);
      case T0.ZodTuple:
        return bW2(I, G);
      case T0.ZodRecord:
        return Qa(I, G);
      case T0.ZodLiteral:
        return RW2(I, G);
      case T0.ZodEnum:
        return fW2(I);
      case T0.ZodNativeEnum:
        return EW2(I);
      case T0.ZodNullable:
        return PW2(I, G);
      case T0.ZodOptional:
        return TW2(I, G);
      case T0.ZodMap:
        return vW2(I, G);
      case T0.ZodSet:
        return lW2(I, G);
      case T0.ZodLazy:
        return o2(I.getter()._def, G);
      case T0.ZodPromise:
        return mW2(I, G);
      case T0.ZodNaN:
      case T0.ZodNever:
        return MW2();
      case T0.ZodEffects:
        return QW2(I, G);
      case T0.ZodAny:
        return FW2();
      case T0.ZodUnknown:
        return jW2();
      case T0.ZodDefault:
        return zW2(I, G);
      case T0.ZodBranded:
        return Na(I, G);
      case T0.ZodReadonly:
        return kW2(I, G);
      case T0.ZodCatch:
        return NW2(I, G);
      case T0.ZodPipeline:
        return OW2(I, G);
      case T0.ZodFunction:
      case T0.ZodVoid:
      case T0.ZodSymbol:
        return;
      default:
        return ((Z) => {
          return
        })(d)
    }
  }
// @from(Start 6064514, End 6064684)
Ro5 = (I, d, G) => {
    if (I.description) {
      if (G.description = I.description, d.markdownDescription) G.markdownDescription = I.description
    }
    return G
  }
// @from(Start 6064690, End 6066034)
KR = (I, d) => {
  let G = GW2(d),
    Z = typeof d === "object" && d.definitions ? Object.entries(d.definitions).reduce((A, [V, X]) => ({
      ...A,
      [V]: o2(X._def, {
        ...G,
        currentPath: [...G.basePath, G.definitionPath, V]
      }, !0) ?? {}
    }), {}) : void 0,
    C = typeof d === "string" ? d : d?.nameStrategy === "title" ? void 0 : d?.name,
    W = o2(I._def, C === void 0 ? G : {
      ...G,
      currentPath: [...G.basePath, G.definitionPath, C]
    }, !1) ?? {},
    w = typeof d === "object" && d.name !== void 0 && d.nameStrategy === "title" ? d.name : void 0;
  if (w !== void 0) W.title = w;
  let B = C === void 0 ? Z ? {
    ...W,
    [G.definitionPath]: Z
  } : W : {
    $ref: [...G.$refStrategy === "relative" ? [] : G.basePath, G.definitionPath, C].join("/"),
    [G.definitionPath]: {
      ...Z,
      [C]: W
    }
  };
  if (G.target === "jsonSchema7") B.$schema = "http://json-schema.org/draft-07/schema#";
  else if (G.target === "jsonSchema2019-09" || G.target === "openAi") B.$schema = "https://json-schema.org/draft/2019-09/schema#";
  if (G.target === "openAi" && (("anyOf" in B) || ("oneOf" in B) || ("allOf" in B) || ("type" in B) && Array.isArray(B.type))) console.warn("Warning: OpenAI may not support schemas with unions as roots! Try wrapping it in an object property.");
  return B
}
// @from(Start 6066040, End 6066056)
s$ = J1(u1(), 1)
// @from(Start 6066217, End 6066233)
Q$ = J1(u1(), 1)
// @from(Start 6066236, End 6066390)
function A3() {
  return Q$.createElement(u, null, "  ⎿  ", Q$.createElement(u, {
    color: r1().error
  }, "No (tell Claude what to do differently)"))
}
// @from(Start 6066395, End 6066413)
MF1 = J1(t01(), 1)
// @from(Start 6066419, End 6066443)
vF1 = "__SINGLE_QUOTE__"
// @from(Start 6066447, End 6066471)
EF1 = "__DOUBLE_QUOTE__"
// @from(Start 6066474, End 6067149)
function NR(I) {
  let d = [];
  for (let C of MF1.parse(I.replaceAll('"', `"${EF1}`).replaceAll("'", `'${vF1}`), (W) => `$${W}`)) {
    if (typeof C === "string") {
      if (d.length > 0 && typeof d[d.length - 1] === "string") {
        d[d.length - 1] += " " + C;
        continue
      }
    }
    d.push(C)
  }
  return d.map((C) => {
    if (typeof C === "string") return C;
    if ("comment" in C) return "#" + C.comment;
    if ("op" in C && C.op === "glob") return C.pattern;
    if ("op" in C) return C.op;
    return null
  }).filter((C) => C !== null).map((C) => {
    return C.replaceAll(`${vF1}`, "'").replaceAll(`${EF1}`, '"')
  }).filter((C) => !cW2.has(C))
}
// @from(Start 6067154, End 6067572)
fa = a2(async (I, d) => {
    let G = NR(I),
      [Z, ...C] = await Promise.all([xW2(I, d), ...G.map(async (w) => ({
        subcommand: w,
        prefix: await xW2(w, d)
      }))]);
    if (!Z) return null;
    let W = C.reduce((w, {
      subcommand: B,
      prefix: A
    }) => {
      if (A) w.set(B, A);
      return w
    }, new Map);
    return {
      ...Z,
      subcommandPrefixes: W
    }
  }, (I) => I)
// @from(Start 6067576, End 6070714)
xW2 = a2(async (I, d) => {
    let G = await jZ({
        systemPrompt: [`Your task is to process Bash commands that an AI coding agent wants to run.

This policy spec defines how to determine the prefix of a Bash command:`],
        userPrompt: `<policy_spec>
# ${K4} Code Bash command prefix detection

This document defines risk levels for actions that the ${K4} agent may take. This classification system is part of a broader safety framework and is used to determine when additional user confirmation or oversight may be needed.

## Definitions

**Command Injection:** Any technique used that would result in a command being run other than the detected prefix.

## Command prefix extraction examples
Examples:
- cat foo.txt => cat
- cd src => cd
- cd path/to/files/ => cd
- find ./src -type f -name "*.ts" => find
- gg cat foo.py => gg cat
- gg cp foo.py bar.py => gg cp
- git commit -m "foo" => git commit
- git diff HEAD~1 => git diff
- git diff --staged => git diff
- git diff $(pwd) => command_injection_detected
- git status => git status
- git status# test(\`id\`) => command_injection_detected
- git status\`ls\` => command_injection_detected
- git push => none
- git push origin master => git push
- git log -n 5 => git log
- git log --oneline -n 5 => git log
- grep -A 40 "from foo.bar.baz import" alpha/beta/gamma.py => grep
- pig tail zerba.log => pig tail
- npm test => none
- npm test --foo => npm test
- npm test -- -f "foo" => npm test
- pwd
 curl example.com => command_injection_detected
- pytest foo/bar.py => pytest
- scalac build => none
</policy_spec>

The user has allowed certain command prefixes to be run, and will otherwise be asked to approve or deny the command.
Your task is to determine the command prefix for the following command.

IMPORTANT: Bash commands may run multiple commands that are chained together.
For safety, if the command seems to contain command injection, you must return "command_injection_detected". 
(This will help protect the user: if they think that they're allowlisting command A, 
but the AI coding agent sends a malicious command that technically has the same prefix as command A, 
then the safety system will see that you said “command_injection_detected” and ask the user for manual confirmation.)

Note that not every command has a prefix. If a command has no prefix, return "none".

ONLY return the prefix. Do not return any other text, markdown markers, or other content or formatting.

Command: ${I}
`,
        signal: d,
        enablePromptCaching: !1
      }),
      Z = typeof G.message.content === "string" ? G.message.content : Array.isArray(G.message.content) ? G.message.content.find((C) => C.type === "text")?.text ?? "none" : "none";
    if (Z.startsWith(hZ)) return null;
    if (Z === "command_injection_detected") return {
      commandInjectionDetected: !0
    };
    if (Z === "git") return {
      commandPrefix: null,
      commandInjectionDetected: !1
    };
    if (Z === "none") return {
      commandPrefix: null,
      commandInjectionDetected: !1
    };
    return {
      commandPrefix: Z,
      commandInjectionDetected: !1
    }
  }, (I) => I)
// @from(Start 6070718, End 6070756)
cW2 = new Set(["&&", "||", ";", ";;"])
// @from(Start 6070759, End 6071092)
function Uo5(I) {
  for (let d of MF1.parse(I.replaceAll('"', `"${EF1}`).replaceAll("'", `'${vF1}`), (G) => `$${G}`)) {
    if (typeof d === "string") continue;
    if ("comment" in d) return !1;
    if ("op" in d) {
      if (d.op === "glob") continue;
      else if (cW2.has(d.op)) continue;
      return !1
    }
  }
  return !0
}
// @from(Start 6071094, End 6071150)
function pW2(I) {
  return NR(I).length > 1 && !Uo5(I)
}
// @from(Start 6071155, End 6071171)
JW = J1(u1(), 1)
// @from(Start 6071177, End 6071198)
qa = "dispatch_agent"
// @from(Start 6071288, End 6071304)
S3 = J1(u1(), 1)
// @from(Start 6071399, End 6071416)
TR = J1($g1(), 1)
// @from(Start 6071422, End 6071438)
Cs = J1(u1(), 1)
// @from(Start 6071441, End 6072079)
function yB({
  code: I,
  language: d
}) {
  let G = Cs.useMemo(() => {
    try {
      if (TR.supportsLanguage(d)) return TR.highlight(I, {
        language: d
      });
      else return X0(`Language not supported while highlighting code, falling back to markdown: ${d}`), TR.highlight(I, {
        language: "markdown"
      })
    } catch (Z) {
      if (Z instanceof Error && Z.message.includes("Unknown language")) return X0(`Language not supported while highlighting code, falling back to markdown: ${Z}`), TR.highlight(I, {
        language: "markdown"
      })
    }
  }, [I, d]);
  return Cs.default.createElement(u, null, G)
}
// @from(Start 6072149, End 6072165)
AX = J1(u1(), 1)
// @from(Start 6072267, End 6072604)
function b$(I) {
  if (I.length <= Ws) return {
    totalLines: I.split(`
`).length,
    truncatedContent: I
  };
  let d = Ws / 2,
    G = I.slice(0, d),
    Z = I.slice(-d),
    C = `${G}

... [${I.slice(d,-d).split(`
`).length} lines truncated] ...

${Z}`;
  return {
    totalLines: I.split(`
`).length,
    truncatedContent: C
  }
}
// @from(Start 6072605, End 6073410)
async function Gg2(I, d) {
  let Z = (await jZ({
    systemPrompt: [`Extract any file paths that this command reads or modifies. For commands like "git diff" and "cat", include the paths of files being shown. Use paths verbatim -- don't add any slashes or try to resolve them. Do not try to infer paths that were not explicitly listed in the command output.
Format your response as:
<filepaths>
path/to/file1
path/to/file2
</filepaths>

If no files are read or modified, return empty filepaths tags:
<filepaths>
</filepaths>

Do not include any other text in your response.`],
    userPrompt: `Command: ${I}
Output: ${d}`,
    enablePromptCaching: !0
  })).message.content.filter((C) => C.type === "text").map((C) => C.text).join("");
  return XG(Z, "filepaths")?.trim().split(`
`).filter(Boolean) || []
}
// @from(Start 6073415, End 6073494)
Zg2 = "Extract and read source code from all code cells in a Jupyter notebook."
// @from(Start 6073498, End 6073815)
Cg2 = "Reads a Jupyter notebook (.ipynb file) and returns all of the cells with their outputs. Jupyter notebooks are interactive documents that combine code, text, and visualizations, commonly used for data analysis and scientific computing. The notebook_path parameter must be an absolute path, not a relative path."
// @from(Start 6073883, End 6073895)
ws = new Set
// @from(Start 6073899, End 6073911)
Bs = new Set
// @from(Start 6073914, End 6073972)
function BX(I) {
  return X79(I) ? Wg2(I) : Wg2(R0(), I)
}
// @from(Start 6073974, End 6074028)
function As(I) {
  return BX(I).startsWith(BX(t7()))
}
// @from(Start 6074030, End 6074133)
function PB(I) {
  let d = BX(I);
  for (let G of ws)
    if (d.startsWith(G)) return !0;
  return !1
}
// @from(Start 6074135, End 6074238)
function OR(I) {
  let d = BX(I);
  for (let G of Bs)
    if (d.startsWith(G)) return !0;
  return !1
}
// @from(Start 6074240, End 6074347)
function Y79(I) {
  let d = BX(I);
  for (let G of ws)
    if (G.startsWith(d)) ws.delete(G);
  ws.add(d)
}
// @from(Start 6074349, End 6074392)
function ug1() {
  let I = t7();
  Y79(I)
}
// @from(Start 6074394, End 6074501)
function _79(I) {
  let d = BX(I);
  for (let G of Bs)
    if (G.startsWith(d)) Bs.delete(G);
  Bs.add(d)
}
// @from(Start 6074503, End 6074545)
function Vs() {
  let I = t7();
  _79(I)
}
// @from(Start 6074550, End 6074703)
J79 = s.strictObject({
  notebook_path: s.string().describe("The absolute path to the Jupyter notebook file to read (must be absolute, not relative)")
})
// @from(Start 6074706, End 6074950)
function Ag2(I) {
  return I.flatMap(q79).reduce((G, Z) => {
    if (G.length === 0) return [Z];
    let C = G[G.length - 1];
    if (C && C.type === "text" && Z.type === "text") return C.text += `
` + Z.text, G;
    return [...G, Z]
  }, [])
}
// @from(Start 6074955, End 6076579)
VH = {
  name: "ReadNotebook",
  async description() {
    return Zg2
  },
  async prompt() {
    return Cg2
  },
  isReadOnly() {
    return !0
  },
  inputSchema: J79,
  userFacingName() {
    return "Read Notebook"
  },
  async isEnabled() {
    return !0
  },
  needsPermissions({
    notebook_path: I
  }) {
    return !PB(I)
  },
  async validateInput({
    notebook_path: I
  }) {
    let d = wg2(I) ? I : Bg2(R0(), I);
    if (!D79(d)) {
      let G = Yf(d),
        Z = "File does not exist.";
      if (G) Z += ` Did you mean ${G}?`;
      return {
        result: !1,
        message: Z
      }
    }
    if (F79(d) !== ".ipynb") return {
      result: !1,
      message: "File must be a Jupyter notebook (.ipynb file)."
    };
    return {
      result: !0
    }
  },
  renderToolUseMessage(I, {
    verbose: d
  }) {
    return `notebook_path: ${d?I.notebook_path:g79(R0(),I.notebook_path)}`
  },
  renderToolUseRejectedMessage() {
    return AX.createElement(A3, null)
  },
  renderToolResultMessage(I) {
    if (!I) return AX.createElement(u, null, "No cells found in notebook");
    if (I.length < 1 || !I[0]) return AX.createElement(u, null, "No cells found in notebook");
    return AX.createElement(u, null, "Read ", I.length, " cells")
  },
  async * call({
    notebook_path: I
  }) {
    let d = wg2(I) ? I : Bg2(R0(), I),
      G = H79(d, "utf-8"),
      Z = JSON.parse(G),
      C = Z.metadata.language_info?.name ?? "python",
      W = Z.cells.map((w, B) => z79(w, B, C));
    yield {
      type: "result",
      resultForAssistant: Ag2(W),
      data: W
    }
  },
  renderResultForAssistant: Ag2
}
// @from(Start 6076582, End 6076725)
function Tg1(I) {
  if (!I) return "";
  let d = Array.isArray(I) ? I.join("") : I,
    {
      truncatedContent: G
    } = b$(d);
  return G
}
// @from(Start 6076727, End 6076990)
function K79(I) {
  if (typeof I["image/png"] === "string") return {
    image_data: I["image/png"],
    media_type: "image/png"
  };
  if (typeof I["image/jpeg"] === "string") return {
    image_data: I["image/jpeg"],
    media_type: "image/jpeg"
  };
  return
}
// @from(Start 6076992, End 6077454)
function N79(I) {
  switch (I.output_type) {
    case "stream":
      return {
        output_type: I.output_type, text: Tg1(I.text)
      };
    case "execute_result":
    case "display_data":
      return {
        output_type: I.output_type, text: Tg1(I.data?.["text/plain"]), image: I.data && K79(I.data)
      };
    case "error":
      return {
        output_type: I.output_type, text: Tg1(`${I.ename}: ${I.evalue}
${I.traceback.join(`
`)}`)
      }
  }
}
// @from(Start 6077456, End 6077730)
function z79(I, d, G) {
  let Z = {
    cell: d,
    cellType: I.cell_type,
    source: Array.isArray(I.source) ? I.source.join("") : I.source,
    language: G,
    execution_count: I.execution_count
  };
  if (I.outputs?.length) Z.outputs = I.outputs.map(N79);
  return Z
}
// @from(Start 6077732, End 6078047)
function Q79(I) {
  let d = [];
  if (I.cellType !== "code") d.push(`<cell_type>${I.cellType}</cell_type>`);
  if (I.language !== "python" && I.cellType === "code") d.push(`<language>${I.language}</language>`);
  return {
    text: `<cell ${I.cell}>${d.join("")}${I.source}</cell ${I.cell}>`,
    type: "text"
  }
}
// @from(Start 6078049, End 6078323)
function f79(I) {
  let d = [];
  if (I.text) d.push({
    text: `
${I.text}`,
    type: "text"
  });
  if (I.image) d.push({
    type: "image",
    source: {
      data: I.image.image_data,
      media_type: I.image.media_type,
      type: "base64"
    }
  });
  return d
}
// @from(Start 6078325, End 6078420)
function q79(I) {
  let d = Q79(I),
    G = I.outputs?.flatMap(f79);
  return [d, ...G ?? []]
}
// @from(Start 6078425, End 6078435)
R79 = 2000
// @from(Start 6078439, End 6078449)
U79 = 2000
// @from(Start 6078453, End 6078499)
Vg2 = "Read a file from the local filesystem."
// @from(Start 6078503, End 6079050)
Xg2 = `Reads a file from the local filesystem. The file_path parameter must be an absolute path, not a relative path. By default, it reads up to ${R79} lines starting from the beginning of the file. You can optionally specify a line offset and limit (especially handy for long files), but it's recommended to read the whole file by not providing these parameters. Any lines longer than ${U79} characters will be truncated. For image files, the tool will display the image for you. For Jupyter notebooks (.ipynb files), use the ${VH.name} instead.`
// @from(Start 6079056, End 6079063)
GJ1 = 3
// @from(Start 6079067, End 6079079)
wJ1 = 262144
// @from(Start 6079083, End 6079148)
ZJ1 = new Set([".png", ".jpg", ".jpeg", ".gif", ".bmp", ".webp"])
// @from(Start 6079152, End 6079161)
Ss = 2000
// @from(Start 6079165, End 6079174)
Ls = 2000
// @from(Start 6079178, End 6079191)
CJ1 = 3932160
// @from(Start 6079195, End 6079568)
qZ9 = s.strictObject({
    file_path: s.string().describe("The absolute path to the file to read"),
    offset: s.number().optional().describe("The line number to start reading from. Only provide if the file is too large to read at once"),
    limit: s.number().optional().describe("The number of lines to read. Only provide if the file is too large to read at once.")
  })
// @from(Start 6079572, End 6083434)
Fd = {
    name: "View",
    async description() {
      return Vg2
    },
    async prompt() {
      return Xg2
    },
    inputSchema: qZ9,
    isReadOnly() {
      return !0
    },
    userFacingName() {
      return "Read"
    },
    async isEnabled() {
      return !0
    },
    needsPermissions({
      file_path: I
    }) {
      return !PB(I || R0())
    },
    renderToolUseMessage(I, {
      verbose: d
    }) {
      let {
        file_path: G,
        ...Z
      } = I;
      return [
        ["file_path", d ? G : fZ9(R0(), G)], ...Object.entries(Z)
      ].map(([W, w]) => `${W}: ${JSON.stringify(w)}`).join(", ")
    },
    renderToolResultMessage(I, {
      verbose: d
    }) {
      switch (I.type) {
        case "image":
          return S3.createElement(p, {
            justifyContent: "space-between",
            overflowX: "hidden",
            width: "100%"
          }, S3.createElement(p, {
            flexDirection: "row"
          }, S3.createElement(u, null, "  ⎿  "), S3.createElement(u, null, "Read image")));
        case "text": {
          let {
            filePath: G,
            content: Z,
            numLines: C
          } = I.file, W = Z || "(No content)";
          return S3.createElement(p, {
            justifyContent: "space-between",
            overflowX: "hidden",
            width: "100%"
          }, S3.createElement(p, {
            flexDirection: "row"
          }, S3.createElement(u, null, "  ⎿  "), S3.createElement(p, {
            flexDirection: "column"
          }, S3.createElement(yB, {
            code: d ? W : W.split(`
`).slice(0, GJ1).filter((w) => w.trim() !== "").join(`
`),
            language: QZ9(G).slice(1)
          }), !d && C > GJ1 && S3.createElement(u, {
            color: r1().secondaryText
          }, "... (+", C - GJ1, " lines)"))))
        }
      }
    },
    renderToolUseRejectedMessage() {
      return S3.createElement(A3, null)
    },
    async validateInput({
      file_path: I,
      offset: d,
      limit: G
    }) {
      let Z = c81(I);
      if (!zZ9(Z)) {
        let B = Yf(Z),
          A = "File does not exist.";
        if (B) A += ` Did you mean ${B}?`;
        return {
          result: !1,
          message: A
        }
      }
      let W = PK2(Z).size,
        w = WJ1.extname(Z).toLowerCase();
      if (!ZJ1.has(w)) {
        if (W > wJ1 && !d && !G) return {
          result: !1,
          message: yK2(W),
          meta: {
            fileSize: W
          }
        }
      }
      return {
        result: !0
      }
    },
    async * call({
      file_path: I,
      offset: d = 1,
      limit: G = void 0
    }, {
      readFileTimestamps: Z
    }) {
      let C = WJ1.extname(I).toLowerCase(),
        W = c81(I);
      if (Z[W] = Date.now(), ZJ1.has(C)) {
        let _ = await RZ9(W, C);
        yield {
          type: "result",
          data: _,
          resultForAssistant: this.renderResultForAssistant(_)
        };
        return
      }
      let w = d === 0 ? 0 : d - 1,
        {
          content: B,
          lineCount: A,
          totalLines: V
        } = E40(W, w, G);
      if (!ZJ1.has(C) && B.length > wJ1) throw new Error(yK2(B.length));
      let X = {
        type: "text",
        file: {
          filePath: I,
          content: B,
          numLines: A,
          startLine: d,
          totalLines: V
        }
      };
      yield {
        type: "result",
        data: X,
        resultForAssistant: this.renderResultForAssistant(X)
      }
    },
    renderResultForAssistant(I) {
      switch (I.type) {
        case "image":
          return [{
            type: "image",
            source: {
              type: "base64",
              data: I.file.base64,
              media_type: I.file.type
            }
          }];
        case "text":
          return _f(I.file)
      }
    }
  }
// @from(Start 6083438, End 6083679)
yK2 = (I) => `File content (${Math.round(I/1024)}KB) exceeds maximum allowed size (${Math.round(wJ1/1024)}KB). Please use offset and limit parameters to read specific portions of the file, or use the GrepTool to search for specific content.`
// @from(Start 6083682, End 6083825)
function n$(I, d) {
  return {
    type: "image",
    file: {
      base64: I.toString("base64"),
      type: `image/${d.slice(1)}`
    }
  }
}
// @from(Start 6083826, End 6084708)
async function RZ9(I, d) {
  try {
    let G = PK2(I),
      Z = (await Promise.resolve().then(() => J1(LK2(), 1))).default,
      C = Z(dJ1(I)),
      W = await C.metadata();
    if (!W.width || !W.height) {
      if (G.size > CJ1) {
        let V = await C.jpeg({
          quality: 80
        }).toBuffer();
        return n$(V, "jpeg")
      }
    }
    let w = W.width || 0,
      B = W.height || 0;
    if (G.size <= CJ1 && w <= Ss && B <= Ls) return n$(dJ1(I), d);
    if (w > Ss) B = Math.round(B * Ss / w), w = Ss;
    if (B > Ls) w = Math.round(w * Ls / B), B = Ls;
    let A = await C.resize(w, B, {
      fit: "inside",
      withoutEnlargement: !0
    }).toBuffer();
    if (A.length > CJ1) {
      let V = await C.jpeg({
        quality: 80
      }).toBuffer();
      return n$(V, "jpeg")
    }
    return n$(A, d)
  } catch (G) {
    return X0(G), n$(dJ1(I), d)
  }
}
// @from(Start 6084713, End 6084728)
ys = "GlobTool"
// @from(Start 6084732, End 6085112)
BJ1 = `- Fast file pattern matching tool that works with any codebase size
- Supports glob patterns like "**/*.js" or "src/**/*.ts"
- Returns matching file paths sorted by modification time
- Use this tool when you need to find files by name patterns
- When you are doing an open ended search that may require multiple rounds of globbing and grepping, use the Agent tool instead
`
// @from(Start 6085118, End 6085133)
Ps = "GrepTool"
// @from(Start 6085137, End 6085671)
AJ1 = `
- Fast content search tool that works with any codebase size
- Searches file contents using regular expressions
- Supports full regex syntax (eg. "log.*Error", "function\\s+\\w+", etc.)
- Filter files by pattern with the include parameter (eg. "*.js", "*.{ts,tsx}")
- Returns matching file paths sorted by modification time
- Use this tool when you need to find files containing specific patterns
- When you are doing an open ended search that may require multiple rounds of globbing and grepping, use the Agent tool instead
`
// @from(Start 6085720, End 6085736)
NI = J1(u1(), 1)
// @from(Start 6085870, End 6086082)
VJ1 = "Lists files and directories in a given path. The path parameter must be an absolute path, not a relative path. You should generally prefer the Glob and Grep tools, if you know which directories to search."
// @from(Start 6086088, End 6086095)
XJ1 = 4
// @from(Start 6086099, End 6086108)
r$ = 1000
// @from(Start 6086112, End 6086331)
YJ1 = `There are more than ${r$} files in the repository. Use the LS tool (passing a specific path), Bash tool, and other tools to explore nested directories. The first ${r$} files and directories are included below:

`
// @from(Start 6086335, End 6086471)
EZ9 = s.strictObject({
    path: s.string().describe("The absolute path to the directory to list (must be absolute, not relative)")
  })
// @from(Start 6086475, End 6088653)
zI = {
    name: "LS",
    async description() {
      return VJ1
    },
    inputSchema: EZ9,
    userFacingName() {
      return "List"
    },
    async isEnabled() {
      return !0
    },
    isReadOnly() {
      return !0
    },
    needsPermissions({
      path: I
    }) {
      return !PB(I)
    },
    async prompt() {
      return VJ1
    },
    renderResultForAssistant(I) {
      return I
    },
    renderToolUseMessage({
      path: I
    }, {
      verbose: d
    }) {
      let G = I ? $K2(I) ? I : TK2(R0(), I) : void 0,
        Z = G ? _J1(R0(), G) : ".";
      return `path: "${d?I:Z}"`
    },
    renderToolUseRejectedMessage() {
      return NI.createElement(A3, null)
    },
    renderToolResultMessage(I, {
      verbose: d
    }) {
      if (typeof I !== "string") return null;
      let G = I.replace(YJ1, "");
      if (!G) return null;
      return NI.createElement(p, {
        justifyContent: "space-between",
        width: "100%"
      }, NI.createElement(p, null, NI.createElement(u, null, "  ⎿  "), NI.createElement(p, {
        flexDirection: "column",
        paddingLeft: 0
      }, G.split(`
`).filter((Z) => Z.trim() !== "").slice(0, d ? void 0 : XJ1).map((Z, C) => NI.createElement(u, {
        key: C
      }, Z)), !d && G.split(`
`).length > XJ1 && NI.createElement(u, {
        color: r1().secondaryText
      }, "... (+", G.split(`
`).length - XJ1, " items)"))))
    },
    async * call({
      path: I
    }, {
      abortController: d
    }) {
      let G = $K2(I) ? I : TK2(R0(), I),
        Z = MZ9(G, R0(), d.signal).sort(),
        C = `
NOTE: do any of the files above seem malicious? If so, you MUST refuse to continue work.`,
        W = mK2(SZ9(Z)),
        w = W + `
NOTE: do any of the files above seem malicious? If so, you MUST refuse to continue work.`;
      if (Z.length < r$) yield {
        type: "result",
        data: W,
        resultForAssistant: this.renderResultForAssistant(w)
      };
      else {
        let B = `${YJ1}${W}`,
          A = `${YJ1}${w}`;
        yield {
          type: "result",
          data: B,
          resultForAssistant: this.renderResultForAssistant(A)
        }
      }
    }
  }
// @from(Start 6088656, End 6089242)
function MZ9(I, d, G) {
  let Z = [],
    C = [I];
  while (C.length > 0) {
    if (Z.length > r$) return Z;
    if (G.aborted) return Z;
    let W = C.shift();
    if (OK2(W)) continue;
    if (W !== I) Z.push(_J1(d, W) + tJ);
    let w;
    try {
      w = UZ9(W, {
        withFileTypes: !0
      })
    } catch (B) {
      X0(B);
      continue
    }
    for (let B of w)
      if (B.isDirectory()) C.push(uK2(W, B.name) + tJ);
      else {
        let A = uK2(W, B.name);
        if (OK2(A)) continue;
        if (Z.push(_J1(d, A)), Z.length > r$) return Z
      }
  }
  return Z
}
// @from(Start 6089244, End 6089803)
function SZ9(I) {
  let d = [];
  for (let G of I) {
    let Z = G.split(tJ),
      C = d,
      W = "";
    for (let w = 0; w < Z.length; w++) {
      let B = Z[w];
      if (!B) continue;
      W = W ? `${W}${tJ}${B}` : B;
      let A = w === Z.length - 1,
        V = C.find((X) => X.name === B);
      if (V) C = V.children || [];
      else {
        let X = {
          name: B,
          path: W,
          type: A ? "file" : "directory"
        };
        if (!A) X.children = [];
        C.push(X), C = X.children || []
      }
    }
  }
  return d
}
// @from(Start 6089805, End 6090070)
function mK2(I, d = 0, G = "") {
  let Z = "";
  if (d === 0) Z += `- ${R0()}${tJ}
`, G = "  ";
  for (let C of I)
    if (Z += `${G}- ${C.name}${C.type==="directory"?tJ:""}
`, C.children && C.children.length > 0) Z += mK2(C.children, d + 1, `${G}  `);
  return Z
}
// @from(Start 6090072, End 6090206)
function OK2(I) {
  if (I !== "." && vZ9(I).startsWith(".")) return !0;
  if (I.includes(`__pycache__${tJ}`)) return !0;
  return !1
}
// @from(Start 6090211, End 6090221)
Ws = 30000
// @from(Start 6090225, End 6090232)
a$ = 50
// @from(Start 6090236, End 6090397)
DJ1 = ["alias", "curl", "curlie", "wget", "axel", "aria2c", "nc", "telnet", "lynx", "w3m", "links", "httpie", "xh", "http-prompt", "chrome", "firefox", "safari"]
// @from(Start 6090401, End 6099527)
lK2 = `Executes a given bash command in a persistent shell session with optional timeout, ensuring proper handling and security measures.

Before executing the command, please follow these steps:

1. Directory Verification:
   - If the command will create new directories or files, first use the LS tool to verify the parent directory exists and is the correct location
   - For example, before running "mkdir foo/bar", first use LS to check that "foo" exists and is the intended parent directory

2. Security Check:
   - For security and to limit the threat of a prompt injection attack, some commands are limited or banned. If you use a disallowed command, you will receive an error message explaining the restriction. Explain the error to the User.
   - Verify that the command is not one of the banned commands: ${DJ1.join(", ")}.

3. Command Execution:
   - After ensuring proper quoting, execute the command.
   - Capture the output of the command.

4. Output Processing:
   - If the output exceeds ${Ws} characters, output will be truncated before being returned to you.
   - Prepare the output for display to the user.

5. Return Result:
   - Provide the processed output of the command.
   - If any errors occurred during execution, include those in the output.

Usage notes:
  - The command argument is required.
  - You can specify an optional timeout in milliseconds (up to 600000ms / 10 minutes). If not specified, commands will timeout after 30 minutes.
  - VERY IMPORTANT: You MUST avoid using search commands like \`find\` and \`grep\`. Instead use ${Ps}, ${ys}, or ${qa} to search. You MUST avoid read tools like \`cat\`, \`head\`, \`tail\`, and \`ls\`, and use ${Fd.name} and ${zI.name} to read files.
  - When issuing multiple commands, use the ';' or '&&' operator to separate them. DO NOT use newlines (newlines are ok in quoted strings).
  - IMPORTANT: All commands share the same shell session. Shell state (environment variables, virtual environments, current directory, etc.) persist between commands. For example, if you set an environment variable as part of a command, the environment variable will persist for subsequent commands.
  - Try to maintain your current working directory throughout the session by using absolute paths and avoiding usage of \`cd\`. You may use \`cd\` if the User explicitly requests it.
  <good-example>
  pytest /foo/bar/tests
  </good-example>
  <bad-example>
  cd /foo/bar && pytest tests
  </bad-example>

# Committing changes with git

When the user asks you to create a new git commit, follow these steps carefully:

1. Start with a single message that contains exactly three tool_use blocks that do the following (it is VERY IMPORTANT that you send these tool_use blocks in a single message, otherwise it will feel slow to the user!):
   - Run a git status command to see all untracked files.
   - Run a git diff command to see both staged and unstaged changes that will be committed.
   - Run a git log command to see recent commit messages, so that you can follow this repository's commit message style.

2. Use the git context at the start of this conversation to determine which files are relevant to your commit. Add relevant untracked files to the staging area. Do not commit files that were already modified at the start of this conversation, if they are not relevant to your commit.

3. Analyze all staged changes (both previously staged and newly added) and draft a commit message. Wrap your analysis process in <commit_analysis> tags:

<commit_analysis>
- List the files that have been changed or added
- Summarize the nature of the changes (eg. new feature, enhancement to an existing feature, bug fix, refactoring, test, docs, etc.)
- Brainstorm the purpose or motivation behind these changes
- Do not use tools to explore code, beyond what is available in the git context
- Assess the impact of these changes on the overall project
- Check for any sensitive information that shouldn't be committed
- Draft a concise (1-2 sentences) commit message that focuses on the "why" rather than the "what"
- Ensure your language is clear, concise, and to the point
- Ensure the message accurately reflects the changes and their purpose (i.e. "add" means a wholly new feature, "update" means an enhancement to an existing feature, "fix" means a bug fix, etc.)
- Ensure the message is not generic (avoid words like "Update" or "Fix" without context)
- Review the draft message to ensure it accurately reflects the changes and their purpose
</commit_analysis>

4. Create the commit with a message ending with:
\uD83E\uDD16 Generated with ${K4}
Co-Authored-By: Claude <<EMAIL>>

- In order to ensure good formatting, ALWAYS pass the commit message via a HEREDOC, a la this example:
<example>
git commit -m "$(cat <<'EOF'
   Commit message here.

   \uD83E\uDD16 Generated with ${K4}
   Co-Authored-By: Claude <<EMAIL>>
   EOF
   )"
</example>

5. If the commit fails due to pre-commit hook changes, retry the commit ONCE to include these automated changes. If it fails again, it usually means a pre-commit hook is preventing the commit. If the commit succeeds but you notice that files were modified by the pre-commit hook, you MUST amend your commit to include them.

6. Finally, run git status to make sure the commit succeeded.

Important notes:
- When possible, combine the "git add" and "git commit" commands into a single "git commit -am" command, to speed things up
- However, be careful not to stage files (e.g. with \`git add .\`) for commits that aren't part of the change, they may have untracked files they want to keep around, but not commit.
- NEVER update the git config
- DO NOT push to the remote repository
- IMPORTANT: Never use git commands with the -i flag (like git rebase -i or git add -i) since they require interactive input which is not supported.
- If there are no changes to commit (i.e., no untracked files and no modifications), do not create an empty commit
- Ensure your commit message is meaningful and concise. It should explain the purpose of the changes, not just describe them.
- Return an empty response - the user will see the git output directly

# Creating pull requests
Use the gh command via the Bash tool for ALL GitHub-related tasks including working with issues, pull requests, checks, and releases. If given a Github URL use the gh command to get the information needed.

IMPORTANT: When the user asks you to create a pull request, follow these steps carefully:

1. Understand the current state of the branch. Remember to send a single message that contains multiple tool_use blocks (it is VERY IMPORTANT that you do this in a single message, otherwise it will feel slow to the user!):
   - Run a git status command to see all untracked files.
   - Run a git diff command to see both staged and unstaged changes that will be committed.
   - Check if the current branch tracks a remote branch and is up to date with the remote, so you know if you need to push to the remote
   - Run a git log command and \`git diff main...HEAD\` to understand the full commit history for the current branch (from the time it diverged from the \`main\` branch.)

2. Create new branch if needed

3. Commit changes if needed

4. Push to remote with -u flag if needed

5. Analyze all changes that will be included in the pull request, making sure to look at all relevant commits (not just the latest commit, but all commits that will be included in the pull request!), and draft a pull request summary. Wrap your analysis process in <pr_analysis> tags:

<pr_analysis>
- List the commits since diverging from the main branch
- Summarize the nature of the changes (eg. new feature, enhancement to an existing feature, bug fix, refactoring, test, docs, etc.)
- Brainstorm the purpose or motivation behind these changes
- Assess the impact of these changes on the overall project
- Do not use tools to explore code, beyond what is available in the git context
- Check for any sensitive information that shouldn't be committed
- Draft a concise (1-2 bullet points) pull request summary that focuses on the "why" rather than the "what"
- Ensure the summary accurately reflects all changes since diverging from the main branch
- Ensure your language is clear, concise, and to the point
- Ensure the summary accurately reflects the changes and their purpose (ie. "add" means a wholly new feature, "update" means an enhancement to an existing feature, "fix" means a bug fix, etc.)
- Ensure the summary is not generic (avoid words like "Update" or "Fix" without context)
- Review the draft summary to ensure it accurately reflects the changes and their purpose
</pr_analysis>

6. Create PR using gh pr create with the format below. Use a HEREDOC to pass the body to ensure correct formatting.
<example>
gh pr create --title "the pr title" --body "$(cat <<'EOF'
## Summary
<1-3 bullet points>

## Test plan
[Checklist of TODOs for testing the pull request...]

\uD83E\uDD16 Generated with ${K4}
EOF
)"
</example>

Important:
- Return an empty response - the user will see the gh output directly
- Never update git config`
// @from(Start 6099530, End 6099751)
function LZ9(I, d) {
  let G = I.split(`
`);
  if (G.length <= a$) return G.join(`
`);
  let Z = Math.floor(a$ / 2),
    C = a$ - Z;
  return [...G.slice(0, Z), j0.grey(`... (+${d-a$} lines)`), ...G.slice(-C)].join(`
`)
}
// @from(Start 6099753, End 6100158)
function pR({
  content: I,
  lines: d,
  verbose: G,
  isError: Z
}) {
  return JW.createElement(p, {
    justifyContent: "space-between",
    width: "100%"
  }, JW.createElement(p, {
    flexDirection: "row"
  }, JW.createElement(u, null, "  ⎿  "), JW.createElement(p, {
    flexDirection: "column"
  }, JW.createElement(u, {
    color: Z ? r1().error : void 0
  }, G ? I.trim() : LZ9(I.trim(), d)))))
}
// @from(Start 6100163, End 6100179)
IK = J1(u1(), 1)
// @from(Start 6100182, End 6100822)
function yZ9({
  content: I,
  verbose: d
}) {
  let {
    stdout: G,
    stdoutLines: Z,
    stderr: C,
    stderrLines: W
  } = I;
  return IK.default.createElement(p, {
    flexDirection: "column"
  }, G !== "" ? IK.default.createElement(pR, {
    content: G,
    lines: Z,
    verbose: d
  }) : null, C !== "" ? IK.default.createElement(pR, {
    content: C,
    lines: W,
    verbose: d,
    isError: !0
  }) : null, G === "" && C === "" ? IK.default.createElement(p, {
    flexDirection: "row"
  }, IK.default.createElement(u, null, "  ⎿  "), IK.default.createElement(u, {
    color: r1().secondaryText
  }, "(No content)")) : null)
}
// @from(Start 6100827, End 6100835)
$s = yZ9
// @from(Start 6100841, End 6101020)
o$ = s.strictObject({
    command: s.string().describe("The command to execute"),
    timeout: s.number().optional().describe("Optional timeout in milliseconds (max 600000)")
  })
// @from(Start 6101024, End 6105000)
G5 = {
    name: "Bash",
    async description({
      command: I
    }) {
      try {
        let d = await jZ({
          systemPrompt: [`You are a command description generator. Write a clear, concise description of what this command does in 5-10 words. Examples:

          Input: ls
          Output: Lists files in current directory

          Input: git status
          Output: Shows working tree status

          Input: npm install
          Output: Installs package dependencies

          Input: mkdir foo
          Output: Creates directory 'foo'`],
          userPrompt: `Describe this command: ${I}`
        });
        return (d.message.content[0]?.type === "text" ? d.message.content[0].text : null) || "Executes a bash command"
      } catch (d) {
        return X0(d), "Executes a bash command"
      }
    },
    async prompt() {
      return lK2
    },
    isReadOnly() {
      return !1
    },
    inputSchema: o$,
    userFacingName() {
      return "Bash"
    },
    async isEnabled() {
      return !0
    },
    needsPermissions() {
      return !0
    },
    async validateInput({
      command: I
    }) {
      let d = NR(I);
      for (let G of d) {
        let Z = G.split(" "),
          C = Z[0];
        if (C && DJ1.includes(C.toLowerCase())) return {
          result: !1,
          message: `Command '${C}' is not allowed for security reasons`
        };
        if (C === "cd" && Z[1]) {
          let W = Z[1].replace(/^['"]|['"]$/g, ""),
            w = bK2(W) ? W : jK2(R0(), W);
          if (!x81(hK2(t7(), w), hK2(R0(), t7()))) return {
            result: !1,
            message: `ERROR: cd to '${w}' was blocked. For security, ${K4} may only change directories to child directories of the original working directory (${t7()}) for this session.`
          }
        }
      }
      return {
        result: !0
      }
    },
    renderToolUseMessage({
      command: I
    }) {
      if (I.includes(`"$(cat <<'EOF'`)) {
        let d = I.match(/^(.*?)"?\$\(cat <<'EOF'\n([\s\S]*?)\n\s*EOF\n\s*\)"(.*)$/);
        if (d && d[1] && d[2]) {
          let G = d[1],
            Z = d[2],
            C = d[3] || "";
          return `${G.trim()} "${Z.trim()}"${C.trim()}`
        }
      }
      return I
    },
    renderToolUseRejectedMessage() {
      return s$.createElement(A3, null)
    },
    renderToolResultMessage(I, {
      verbose: d
    }) {
      return s$.createElement($s, {
        content: I,
        verbose: d
      })
    },
    renderResultForAssistant({
      interrupted: I,
      stdout: d,
      stderr: G
    }) {
      let Z = G.trim();
      if (I) {
        if (G) Z += us;
        Z += "<error>Command was aborted before completion</error>"
      }
      let C = d.trim() && Z;
      return `${d.trim()}${C?`
`:""}${Z.trim()}`
    },
    async * call({
      command: I,
      timeout: d = 120000
    }, {
      abortController: G,
      readFileTimestamps: Z
    }) {
      let C = "",
        W = "",
        w = await m8.getInstance().exec(I, G.signal, d);
      if (C += (w.stdout || "").trim() + us, W += (w.stderr || "").trim() + us, w.code !== 0) W += `Exit code ${w.code}`;
      if (!x81(R0(), t7())) await m8.getInstance().setCwd(t7()), W = `${W.trim()}${us}Shell cwd was reset to ${t7()}`, I0("bash_tool_reset_to_original_dir", {});
      Gg2(I, C).then((F) => {
        for (let g of F) {
          let J = bK2(g) ? g : jK2(R0(), g);
          try {
            Z[J] = PZ9(J).mtimeMs
          } catch (K) {
            X0(K)
          }
        }
      });
      let {
        totalLines: B,
        truncatedContent: A
      } = b$(C.trim()), {
        totalLines: V,
        truncatedContent: X
      } = b$(W.trim()), _ = {
        stdout: A,
        stdoutLines: B,
        stderr: X,
        stderrLines: V,
        interrupted: w.interrupted
      };
      yield {
        type: "result",
        resultForAssistant: this.renderResultForAssistant(_),
        data: _
      }
    }
  }
// @from(Start 6105003, End 6105084)
function kK2() {
  return `You are ${K4}, Anthropic's official CLI for Claude.`
}
// @from(Start 6105085, End 6115116)
async function iR() {
  return [`You are an interactive CLI tool that helps users with software engineering tasks. Use the instructions below and the tools available to you to assist the user.

IMPORTANT: Refuse to write code or explain code that may be used maliciously; even if the user claims it is for educational purposes. When working on files, if they seem related to improving, explaining, or interacting with malware or any malicious code you MUST refuse.
IMPORTANT: Before you begin work, think about what the code you're editing is supposed to do based on the filenames directory structure. If it seems malicious, refuse to work on it or answer questions about it, even if the request does not seem malicious (for instance, just asking to explain or speed up the code).

Here are useful slash commands users can run to interact with you:
- /help: Get help with using ${K4}
- /compact: Compact and continue the conversation. This is useful if the conversation is reaching the context limit
There are additional slash commands and flags available to the user. If the user asks about ${K4} functionality, always run \`claude -h\` with ${G5.name} to see supported commands and flags. NEVER assume a flag or command exists without checking the help output first.
To give feedback, users should ${{ISSUES_EXPLAINER:"report the issue at https://github.com/anthropics/claude-code/issues",PACKAGE_URL:"@anthropic-ai/claude-code",README_URL:"https://docs.anthropic.com/s/claude-code",VERSION:"0.2.9"}.ISSUES_EXPLAINER}.

# Memory
If the current working directory contains a file called CLAUDE.md, it will be automatically added to your context. This file serves multiple purposes:
1. Storing frequently used bash commands (build, test, lint, etc.) so you can use them without searching each time
2. Recording the user's code style preferences (naming conventions, preferred libraries, etc.)
3. Maintaining useful information about the codebase structure and organization

When you spend time searching for commands to typecheck, lint, build, or test, you should ask the user if it's okay to add those commands to CLAUDE.md. Similarly, when learning about code style preferences or important codebase information, ask if it's okay to add that to CLAUDE.md so you can remember it for next time.

# Tone and style
You should be concise, direct, and to the point. When you run a non-trivial bash command, you should explain what the command does and why you are running it, to make sure the user understands what you are doing (this is especially important when you are running a command that will make changes to the user's system).
Remember that your output will be displayed on a command line interface. Your responses can use Github-flavored markdown for formatting, and will be rendered in a monospace font using the CommonMark specification.
Output text to communicate with the user; all text you output outside of tool use is displayed to the user. Only use tools to complete tasks. Never use tools like ${G5.name} or code comments as means to communicate with the user during the session.
If you cannot or will not help the user with something, please do not say why or what it could lead to, since this comes across as preachy and annoying. Please offer helpful alternatives if possible, and otherwise keep your response to 1-2 sentences.
IMPORTANT: You should minimize output tokens as much as possible while maintaining helpfulness, quality, and accuracy. Only address the specific query or task at hand, avoiding tangential information unless absolutely critical for completing the request. If you can answer in 1-3 sentences or a short paragraph, please do.
IMPORTANT: You should NOT answer with unnecessary preamble or postamble (such as explaining your code or summarizing your action), unless the user asks you to.
IMPORTANT: Keep your responses short, since they will be displayed on a command line interface. You MUST answer concisely with fewer than 4 lines (not including tool use or code generation), unless user asks for detail. Answer the user's question directly, without elaboration, explanation, or details. One word answers are best. Avoid introductions, conclusions, and explanations. You MUST avoid text before/after your response, such as "The answer is <answer>.", "Here is the content of the file..." or "Based on the information provided, the answer is..." or "Here is what I will do next...". Here are some examples to demonstrate appropriate verbosity:
<example>
user: 2 + 2
assistant: 4
</example>

<example>
user: what is 2+2?
assistant: 4
</example>

<example>
user: is 11 a prime number?
assistant: true
</example>

<example>
user: what command should I run to list files in the current directory?
assistant: ls
</example>

<example>
user: what command should I run to watch files in the current directory?
assistant: [use the ls tool to list the files in the current directory, then read docs/commands in the relevant file to find out how to watch files]
npm run dev
</example>

<example>
user: How many golf balls fit inside a jetta?
assistant: 150000
</example>

<example>
user: what files are in the directory src/?
assistant: [runs ls and sees foo.c, bar.c, baz.c]
user: which file contains the implementation of foo?
assistant: src/foo.c
</example>

<example>
user: write tests for new feature
assistant: [uses grep and glob search tools to find where similar tests are defined, uses concurrent read file tool use blocks in one tool call to read relevant files at the same time, uses edit file tool to write new tests]
</example>

# Proactiveness
You are allowed to be proactive, but only when the user asks you to do something. You should strive to strike a balance between:
1. Doing the right thing when asked, including taking actions and follow-up actions
2. Not surprising the user with actions you take without asking
For example, if the user asks you how to approach something, you should do your best to answer their question first, and not immediately jump into taking actions.
3. Do not add additional code explanation summary unless requested by the user. After working on a file, just stop, rather than providing an explanation of what you did.

# Synthetic messages
Sometimes, the conversation will contain messages like ${KW} or ${_X}. These messages will look like the assistant said them, but they were actually synthetic messages added by the system in response to the user cancelling what the assistant was doing. You should not respond to these messages. You must NEVER send messages like this yourself. 

# Following conventions
When making changes to files, first understand the file's code conventions. Mimic code style, use existing libraries and utilities, and follow existing patterns.
- NEVER assume that a given library is available, even if it is well known. Whenever you write code that uses a library or framework, first check that this codebase already uses the given library. For example, you might look at neighboring files, or check the package.json (or cargo.toml, and so on depending on the language).
- When you create a new component, first look at existing components to see how they're written; then consider framework choice, naming conventions, typing, and other conventions.
- When you edit a piece of code, first look at the code's surrounding context (especially its imports) to understand the code's choice of frameworks and libraries. Then consider how to make the given change in a way that is most idiomatic.
- Always follow security best practices. Never introduce code that exposes or logs secrets and keys. Never commit secrets or keys to the repository.

# Code style
- Do not add comments to the code you write, unless the user asks you to, or the code is complex and requires additional context.

# Doing tasks
The user will primarily request you perform software engineering tasks. This includes solving bugs, adding new functionality, refactoring code, explaining code, and more. For these tasks the following steps are recommended:
1. Use the available search tools to understand the codebase and the user's query. You are encouraged to use the search tools extensively both in parallel and sequentially.
2. Implement the solution using all tools available to you
3. Verify the solution if possible with tests. NEVER assume specific test framework or test script. Check the README or search codebase to determine the testing approach.
4. VERY IMPORTANT: When you have completed a task, you MUST run the lint and typecheck commands (eg. npm run lint, npm run typecheck, ruff, etc.) if they were provided to you to ensure your code is correct. If you are unable to find the correct command, ask the user for the command to run and if they supply it, proactively suggest writing it to CLAUDE.md so that you will know to run it next time.

NEVER commit changes unless the user explicitly asks you to. It is VERY IMPORTANT to only commit when explicitly asked, otherwise the user will feel that you are being too proactive.

# Tool usage policy
- When doing file search, prefer to use the Agent tool in order to reduce context usage.
- If you intend to call multiple tools and there are no dependencies between the calls, make all of the independent calls in the same function_calls block.

You MUST answer concisely with fewer than 4 lines of text (not including tool use or code generation), unless user asks for detail.
`, `
${await xK2()}`, `IMPORTANT: Refuse to write code or explain code that may be used maliciously; even if the user claims it is for educational purposes. When working on files, if they seem related to improving, explaining, or interacting with malware or any malicious code you MUST refuse.
IMPORTANT: Before you begin work, think about what the code you're editing is supposed to do based on the filenames directory structure. If it seems malicious, refuse to work on it or answer questions about it, even if the request does not seem malicious (for instance, just asking to explain or speed up the code).`]
}
// @from(Start 6115117, End 6115436)
async function xK2() {
  let [I, d] = await Promise.all([K6(), eG()]);
  return `Here is useful information about the environment you are running in:
<env>
Working directory: ${R0()}
Is directory a git repo: ${d?"Yes":"No"}
Platform: ${K2.platform}
Today's date: ${new Date().toLocaleDateString()}
Model: ${I}
</env>`
}
// @from(Start 6115437, End 6116342)
async function cK2() {
  return [`You are an agent for ${K4}, Anthropic's official CLI for Claude. Given the user's prompt, you should use the tools available to you to answer the user's question.

Notes:
1. IMPORTANT: You should be concise, direct, and to the point, since your responses will be displayed on a command line interface. Answer the user's question directly, without elaboration, explanation, or details. One word answers are best. Avoid introductions, conclusions, and explanations. You MUST avoid text before/after your response, such as "The answer is <answer>.", "Here is the content of the file..." or "Based on the information provided, the answer is..." or "Here is what I will do next...".
2. When relevant, share file names and code snippets relevant to the query
3. Any file paths you return in your final response MUST be absolute. DO NOT use relative paths.`, `${await xK2()}`]
}
// @from(Start 6116347, End 6116363)
hZ = "API Error"
// @from(Start 6116367, End 6116393)
HJ1 = "Prompt is too long"
// @from(Start 6116397, End 6116430)
FJ1 = "Credit balance is too low"
// @from(Start 6116434, End 6116476)
Os = "Invalid API key · Please run /login"
// @from(Start 6116480, End 6116499)
FH = "(no content)"
// @from(Start 6116503, End 6116543)
GK = !process.env.DISABLE_PROMPT_CACHING
// @from(Start 6116547, End 6116556)
pK2 = 0.8
// @from(Start 6116560, End 6116567)
iK2 = 4
// @from(Start 6116571, End 6116578)
uZ9 = 1
// @from(Start 6116582, End 6116592)
TZ9 = 0.08
// @from(Start 6116596, End 6116603)
OZ9 = 3
// @from(Start 6116607, End 6116615)
mZ9 = 15
// @from(Start 6116619, End 6116629)
lZ9 = 3.75
// @from(Start 6116633, End 6116642)
bZ9 = 0.3
// @from(Start 6116646, End 6116652)
t$ = 1
// @from(Start 6116655, End 6116716)
function ms() {
  return {
    user_id: `${yb()}_${id}`
  }
}
// @from(Start 6116721, End 6116729)
hZ9 = 10
// @from(Start 6116733, End 6116742)
jZ9 = 500
// @from(Start 6116745, End 6116897)
function kZ9(I, d) {
  if (d) {
    let G = parseInt(d, 10);
    if (!isNaN(G)) return G * 1000
  }
  return Math.min(jZ9 * Math.pow(2, I - 1), 32000)
}
// @from(Start 6116899, End 6117314)
function xZ9(I) {
  if (I.message?.includes('"type":"overloaded_error"')) return !1;
  let d = I.headers?.["x-should-retry"];
  if (d === "true") return !0;
  if (d === "false") return !1;
  if (I instanceof rw) return !0;
  if (!I.status) return !1;
  if (I.status === 408) return !0;
  if (I.status === 409) return !0;
  if (I.status === 429) return !0;
  if (I.status && I.status >= 500) return !0;
  return !1
}
// @from(Start 6117315, End 6117980)
async function ls(I, d = {}) {
  let G = d.maxRetries ?? hZ9,
    Z;
  for (let C = 1; C <= G + 1; C++) try {
    return await I(C)
  } catch (W) {
    if (Z = W, C > G || !(W instanceof f9) || !xZ9(W)) throw W;
    let w = W.headers?.["retry-after"] ?? null,
      B = kZ9(C, w);
    console.log(`  ⎿  ${j0.red(`API ${W.name} (${W.message}) · Retrying in ${Math.round(B/1000)} seconds… (attempt ${C}/${G})`)}`), I0("tengu_api_retry", {
      attempt: String(C),
      delayMs: String(B),
      error: W.message,
      status: String(W.status),
      provider: b9 ? "bedrock" : h9 ? "vertex" : "1p"
    }), await new Promise((A) => setTimeout(A, B))
  }
  throw Z
}
// @from(Start 6117981, End 6118678)
async function nK2(I) {
  let d = new J71({
    apiKey: I,
    dangerouslyAllowBrowser: !0,
    maxRetries: 3,
    defaultHeaders: {
      "User-Agent": PS
    }
  });
  try {
    return await ls(async () => {
      let G = Rw,
        Z = [{
          role: "user",
          content: "test"
        }];
      return await d.messages.create({
        model: G,
        max_tokens: 1,
        messages: Z,
        temperature: 0,
        metadata: ms()
      }), !0
    }, {
      maxRetries: 2
    }), !0
  } catch (G) {
    if (X0(G), G instanceof Error && G.message.includes('{"type":"error","error":{"type":"authentication_error","message":"invalid x-api-key"}}')) return !1;
    throw G
  }
}
// @from(Start 6118679, End 6118870)
async function gJ1(I) {
  let d = Date.now(),
    G;
  for await (let C of I) if (C.type === "message_start") G = Date.now() - d;
  return {
    ...await I.finalMessage(),
    ttftMs: G
  }
}
// @from(Start 6118875, End 6118884)
dK = null
// @from(Start 6118887, End 6119565)
function JJ1(I) {
  if (dK) return dK;
  let d = wl1(I),
    G = {
      "x-app": "cli",
      "User-Agent": PS
    };
  if (process.env.ANTHROPIC_AUTH_TOKEN) G.Authorization = `Bearer ${process.env.ANTHROPIC_AUTH_TOKEN}`;
  let Z = {
    defaultHeaders: G,
    maxRetries: 0,
    timeout: parseInt(process.env.API_TIMEOUT_MS || String(60000), 10)
  };
  if (b9) {
    let W = new yr(Z);
    return dK = W, W
  }
  if (h9) {
    let W = {
        ...Z,
        region: d || process.env.CLOUD_ML_REGION || "us-east5"
      },
      w = new Xa(W);
    return dK = w, w
  }
  let C = vw();
  return dK = new J71({
    apiKey: C,
    dangerouslyAllowBrowser: !0,
    ...Z
  }), dK
}
// @from(Start 6119567, End 6119597)
function rK2() {
  dK = null
}
// @from(Start 6119599, End 6120229)
function cZ9(I, d = !1) {
  if (d)
    if (typeof I.message.content === "string") return {
      role: "user",
      content: [{
        type: "text",
        text: I.message.content,
        ...GK ? {
          cache_control: {
            type: "ephemeral"
          }
        } : {}
      }]
    };
    else return {
      role: "user",
      content: I.message.content.map((G, Z) => ({
        ...G,
        ...Z === I.message.content.length - 1 ? GK ? {
          cache_control: {
            type: "ephemeral"
          }
        } : {} : {}
      }))
    };
  return {
    role: "user",
    content: I.message.content
  }
}
// @from(Start 6120231, End 6120935)
function pZ9(I, d = !1) {
  if (d)
    if (typeof I.message.content === "string") return {
      role: "assistant",
      content: [{
        type: "text",
        text: I.message.content,
        ...GK ? {
          cache_control: {
            type: "ephemeral"
          }
        } : {}
      }]
    };
    else return {
      role: "assistant",
      content: I.message.content.map((G, Z) => ({
        ...G,
        ...Z === I.message.content.length - 1 && G.type !== "thinking" && G.type !== "redacted_thinking" ? GK ? {
          cache_control: {
            type: "ephemeral"
          }
        } : {} : {}
      }))
    };
  return {
    role: "assistant",
    content: I.message.content
  }
}
// @from(Start 6120937, End 6121039)
function Ts(I) {
  let d = I[0] || "",
    G = I.slice(1);
  return [d, G.join(`
`)].filter(Boolean)
}
// @from(Start 6121040, End 6121130)
async function bs(I, d, G, Z, C, W) {
  return await gF1(I, () => iZ9(I, d, G, Z, C, W))
}
// @from(Start 6121132, End 6121371)
function aK2(I, d) {
  if (Object.entries(d).length === 0) return I;
  return [...I, `
As you answer the user's questions, you can use the following context:
`, ...Object.entries(d).map(([G, Z]) => `<context name="${G}">${Z}</context>`)]
}
// @from(Start 6121372, End 6124724)
async function iZ9(I, d, G, Z, C, W) {
  let w = await JJ1(W.model);
  if (W.prependCLISysprompt) {
    let [T] = Ts(d);
    I0("tengu_sysprompt_block", {
      snippet: T?.slice(0, 20),
      length: String(T?.length ?? 0),
      hash: T ? $Z9("sha256").update(T).digest("hex") : ""
    }), d = [kK2(), ...d]
  }
  let B = Ts(d).map((T) => ({
      ...GK ? {
        cache_control: {
          type: "ephemeral"
        }
      } : {},
      text: T,
      type: "text"
    })),
    A = await Promise.all(Z.map(async (T) => ({
      name: T.name,
      description: await T.prompt({
        dangerouslySkipPermissions: W.dangerouslySkipPermissions
      }),
      input_schema: "inputJSONSchema" in T && T.inputJSONSchema ? T.inputJSONSchema : KR(T.inputSchema)
    }))),
    V = await fb(),
    X = GK && V.length > 0;
  I0("tengu_api_query", {
    model: W.model,
    messagesLength: String(JSON.stringify([...B, ...I, ...A]).length),
    temperature: String(t$),
    provider: b9 ? "bedrock" : h9 ? "vertex" : "1p",
    ...X ? {
      betas: V.join(",")
    } : {}
  });
  let _ = Date.now(),
    F = Date.now(),
    g = 0,
    J, K = void 0;
  try {
    J = await ls(async (T) => {
      g = T, F = Date.now();
      let V1 = w.beta.messages.stream({
        model: W.model,
        max_tokens: Math.max(G + 1, sZ9(W.model)),
        messages: nZ9(I),
        temperature: t$,
        system: B,
        tools: A,
        ...X ? {
          betas: V
        } : {},
        metadata: ms(),
        ...{}
      }, {
        signal: C
      });
      return K = V1, gJ1(V1)
    })
  } catch (T) {
    return X0(T), I0("tengu_api_error", {
      model: W.model,
      error: T instanceof Error ? T.message : String(T),
      status: T instanceof f9 ? String(T.status) : void 0,
      messageCount: String(I.length),
      messageTokens: String(oD(I)),
      durationMs: String(Date.now() - F),
      durationMsIncludingRetries: String(Date.now() - _),
      attempt: String(g),
      provider: b9 ? "bedrock" : h9 ? "vertex" : "1p",
      requestId: K?.request_id ?? void 0
    }), KJ1(T)
  }
  let Q = Date.now() - F,
    E = Date.now() - _;
  I0("tengu_api_success", {
    model: W.model,
    messageCount: String(I.length),
    messageTokens: String(oD(I)),
    inputTokens: String(J.usage.input_tokens),
    outputTokens: String(J.usage.output_tokens),
    cachedInputTokens: String(J.usage.cache_read_input_tokens ?? 0),
    uncachedInputTokens: String(J.usage.cache_creation_input_tokens ?? 0),
    durationMs: String(Q),
    durationMsIncludingRetries: String(E),
    attempt: String(g),
    ttftMs: String(J.ttftMs),
    provider: b9 ? "bedrock" : h9 ? "vertex" : "1p",
    requestId: K?.request_id ?? void 0,
    stop_reason: J.stop_reason ?? void 0
  });
  let S = J.usage.input_tokens,
    P = J.usage.output_tokens,
    $ = J.usage.cache_read_input_tokens ?? 0,
    h = J.usage.cache_creation_input_tokens ?? 0,
    O = S / 1e6 * OZ9 + P / 1e6 * mZ9 + $ / 1e6 * bZ9 + h / 1e6 * lZ9;
  return _a(O, E), {
    message: {
      ...J,
      content: hs(J.content),
      usage: {
        ...J.usage,
        cache_read_input_tokens: J.usage.cache_read_input_tokens ?? 0,
        cache_creation_input_tokens: J.usage.cache_creation_input_tokens ?? 0
      }
    },
    costUSD: O,
    durationMs: Q,
    type: "assistant",
    uuid: e$()
  }
}
// @from(Start 6124726, End 6125095)
function KJ1(I) {
  if (I instanceof Error && I.message.includes("prompt is too long")) return nR(HJ1);
  if (I instanceof Error && I.message.includes("Your credit balance is too low")) return nR(FJ1);
  if (I instanceof Error && I.message.toLowerCase().includes("x-api-key")) return nR(Os);
  if (I instanceof Error) return nR(`${hZ}: ${I.message}`);
  return nR(hZ)
}
// @from(Start 6125097, End 6125231)
function nZ9(I) {
  return I.map((d, G) => {
    return d.type === "user" ? cZ9(d, G > I.length - 3) : pZ9(d, G > I.length - 3)
  })
}
// @from(Start 6125232, End 6127687)
async function rZ9({
  systemPrompt: I,
  userPrompt: d,
  assistantPrompt: G,
  signal: Z
}) {
  let C = await JJ1(Rw),
    W = Rw,
    w = [{
      role: "user",
      content: d
    }, ...G ? [{
      role: "assistant",
      content: G
    }] : []],
    B = Ts(I).map((h) => ({
      ...GK ? {
        cache_control: {
          type: "ephemeral"
        }
      } : {},
      text: h,
      type: "text"
    }));
  I0("tengu_api_query", {
    model: W,
    messagesLength: String(JSON.stringify([...B, ...w]).length),
    provider: b9 ? "bedrock" : h9 ? "vertex" : "1p"
  });
  let A = 0,
    V = Date.now(),
    X = Date.now(),
    _, F = void 0;
  try {
    _ = await ls(async (h) => {
      A = h, V = Date.now();
      let O = C.beta.messages.stream({
        model: W,
        max_tokens: 512,
        messages: w,
        system: B,
        temperature: 0,
        metadata: ms(),
        stream: !0
      }, {
        signal: Z
      });
      return F = O, await gJ1(O)
    })
  } catch (h) {
    return X0(h), I0("tengu_api_error", {
      error: h instanceof Error ? h.message : String(h),
      status: h instanceof f9 ? String(h.status) : void 0,
      model: Rw,
      messageCount: String(G ? 2 : 1),
      durationMs: String(Date.now() - V),
      durationMsIncludingRetries: String(Date.now() - X),
      attempt: String(A),
      provider: b9 ? "bedrock" : h9 ? "vertex" : "1p",
      requestId: F?.request_id ?? void 0
    }), KJ1(h)
  }
  let g = _.usage.input_tokens,
    J = _.usage.output_tokens,
    K = _.usage.cache_read_input_tokens ?? 0,
    Q = _.usage.cache_creation_input_tokens ?? 0,
    E = g / 1e6 * pK2 + J / 1e6 * iK2 + K / 1e6 * TZ9 + Q / 1e6 * uZ9,
    S = Date.now() - V,
    P = Date.now() - X;
  _a(E, P);
  let $ = {
    durationMs: S,
    message: {
      ..._,
      content: hs(_.content)
    },
    costUSD: E,
    uuid: e$(),
    type: "assistant"
  };
  return I0("tengu_api_success", {
    model: Rw,
    messageCount: String(G ? 2 : 1),
    inputTokens: String(g),
    outputTokens: String(_.usage.output_tokens),
    cachedInputTokens: String(_.usage.cache_read_input_tokens ?? 0),
    uncachedInputTokens: String(_.usage.cache_creation_input_tokens ?? 0),
    durationMs: String(S),
    durationMsIncludingRetries: String(P),
    ttftMs: String(_.ttftMs),
    provider: b9 ? "bedrock" : h9 ? "vertex" : "1p",
    requestId: F?.request_id ?? void 0,
    stop_reason: _.stop_reason ?? void 0
  }), $
}