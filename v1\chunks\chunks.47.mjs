
// @from(Start 4943977, End 4951637)
UR = Y((eg3, rD2) => {
  var M1 = Aw2();
  M1.registerLanguage("1c", Xw2());
  M1.registerLanguage("abnf", _w2());
  M1.registerLanguage("accesslog", Fw2());
  M1.registerLanguage("actionscript", Jw2());
  M1.registerLanguage("ada", Nw2());
  M1.registerLanguage("angelscript", Qw2());
  M1.registerLanguage("apache", qw2());
  M1.registerLanguage("applescript", Mw2());
  M1.registerLanguage("arcade", Lw2());
  M1.registerLanguage("arduino", Pw2());
  M1.registerLanguage("armasm", uw2());
  M1.registerLanguage("xml", lw2());
  M1.registerLanguage("asciidoc", jw2());
  M1.registerLanguage("aspectj", xw2());
  M1.registerLanguage("autohotkey", pw2());
  M1.registerLanguage("autoit", nw2());
  M1.registerLanguage("avrasm", aw2());
  M1.registerLanguage("awk", ow2());
  M1.registerLanguage("axapta", tw2());
  M1.registerLanguage("bash", dB2());
  M1.registerLanguage("basic", ZB2());
  M1.registerLanguage("bnf", WB2());
  M1.registerLanguage("brainfuck", BB2());
  M1.registerLanguage("c-like", VB2());
  M1.registerLanguage("c", YB2());
  M1.registerLanguage("cal", DB2());
  M1.registerLanguage("capnproto", FB2());
  M1.registerLanguage("ceylon", JB2());
  M1.registerLanguage("clean", NB2());
  M1.registerLanguage("clojure", QB2());
  M1.registerLanguage("clojure-repl", qB2());
  M1.registerLanguage("cmake", UB2());
  M1.registerLanguage("coffeescript", EB2());
  M1.registerLanguage("coq", SB2());
  M1.registerLanguage("cos", yB2());
  M1.registerLanguage("cpp", $B2());
  M1.registerLanguage("crmsh", TB2());
  M1.registerLanguage("crystal", mB2());
  M1.registerLanguage("csharp", bB2());
  M1.registerLanguage("csp", jB2());
  M1.registerLanguage("css", xB2());
  M1.registerLanguage("d", pB2());
  M1.registerLanguage("markdown", nB2());
  M1.registerLanguage("dart", aB2());
  M1.registerLanguage("delphi", oB2());
  M1.registerLanguage("diff", tB2());
  M1.registerLanguage("django", dA2());
  M1.registerLanguage("dns", ZA2());
  M1.registerLanguage("dockerfile", WA2());
  M1.registerLanguage("dos", BA2());
  M1.registerLanguage("dsconfig", VA2());
  M1.registerLanguage("dts", YA2());
  M1.registerLanguage("dust", DA2());
  M1.registerLanguage("ebnf", FA2());
  M1.registerLanguage("elixir", JA2());
  M1.registerLanguage("elm", NA2());
  M1.registerLanguage("ruby", fA2());
  M1.registerLanguage("erb", RA2());
  M1.registerLanguage("erlang-repl", vA2());
  M1.registerLanguage("erlang", MA2());
  M1.registerLanguage("excel", LA2());
  M1.registerLanguage("fix", PA2());
  M1.registerLanguage("flix", uA2());
  M1.registerLanguage("fortran", OA2());
  M1.registerLanguage("fsharp", lA2());
  M1.registerLanguage("gams", hA2());
  M1.registerLanguage("gauss", kA2());
  M1.registerLanguage("gcode", cA2());
  M1.registerLanguage("gherkin", iA2());
  M1.registerLanguage("glsl", rA2());
  M1.registerLanguage("gml", sA2());
  M1.registerLanguage("go", eA2());
  M1.registerLanguage("golo", IV2());
  M1.registerLanguage("gradle", GV2());
  M1.registerLanguage("groovy", CV2());
  M1.registerLanguage("haml", wV2());
  M1.registerLanguage("handlebars", VV2());
  M1.registerLanguage("haskell", YV2());
  M1.registerLanguage("haxe", DV2());
  M1.registerLanguage("hsp", FV2());
  M1.registerLanguage("htmlbars", KV2());
  M1.registerLanguage("http", zV2());
  M1.registerLanguage("hy", fV2());
  M1.registerLanguage("inform7", RV2());
  M1.registerLanguage("ini", MV2());
  M1.registerLanguage("irpf90", LV2());
  M1.registerLanguage("isbl", PV2());
  M1.registerLanguage("java", uV2());
  M1.registerLanguage("javascript", mV2());
  M1.registerLanguage("jboss-cli", bV2());
  M1.registerLanguage("json", jV2());
  M1.registerLanguage("julia", xV2());
  M1.registerLanguage("julia-repl", pV2());
  M1.registerLanguage("kotlin", nV2());
  M1.registerLanguage("lasso", aV2());
  M1.registerLanguage("latex", oV2());
  M1.registerLanguage("ldif", tV2());
  M1.registerLanguage("leaf", dX2());
  M1.registerLanguage("less", WX2());
  M1.registerLanguage("lisp", BX2());
  M1.registerLanguage("livecodeserver", VX2());
  M1.registerLanguage("livescript", YX2());
  M1.registerLanguage("llvm", DX2());
  M1.registerLanguage("lsl", FX2());
  M1.registerLanguage("lua", JX2());
  M1.registerLanguage("makefile", NX2());
  M1.registerLanguage("mathematica", RX2());
  M1.registerLanguage("matlab", vX2());
  M1.registerLanguage("maxima", MX2());
  M1.registerLanguage("mel", LX2());
  M1.registerLanguage("mercury", PX2());
  M1.registerLanguage("mipsasm", uX2());
  M1.registerLanguage("mizar", OX2());
  M1.registerLanguage("perl", hX2());
  M1.registerLanguage("mojolicious", kX2());
  M1.registerLanguage("monkey", cX2());
  M1.registerLanguage("moonscript", iX2());
  M1.registerLanguage("n1ql", rX2());
  M1.registerLanguage("nginx", sX2());
  M1.registerLanguage("nim", eX2());
  M1.registerLanguage("nix", IY2());
  M1.registerLanguage("node-repl", GY2());
  M1.registerLanguage("nsis", CY2());
  M1.registerLanguage("objectivec", wY2());
  M1.registerLanguage("ocaml", AY2());
  M1.registerLanguage("openscad", XY2());
  M1.registerLanguage("oxygene", _Y2());
  M1.registerLanguage("parser3", HY2());
  M1.registerLanguage("pf", gY2());
  M1.registerLanguage("pgsql", KY2());
  M1.registerLanguage("php", zY2());
  M1.registerLanguage("php-template", fY2());
  M1.registerLanguage("plaintext", RY2());
  M1.registerLanguage("pony", vY2());
  M1.registerLanguage("powershell", MY2());
  M1.registerLanguage("processing", LY2());
  M1.registerLanguage("profile", PY2());
  M1.registerLanguage("prolog", uY2());
  M1.registerLanguage("properties", OY2());
  M1.registerLanguage("protobuf", lY2());
  M1.registerLanguage("puppet", hY2());
  M1.registerLanguage("purebasic", kY2());
  M1.registerLanguage("python", cY2());
  M1.registerLanguage("python-repl", iY2());
  M1.registerLanguage("q", rY2());
  M1.registerLanguage("qml", sY2());
  M1.registerLanguage("r", eY2());
  M1.registerLanguage("reasonml", I_2());
  M1.registerLanguage("rib", G_2());
  M1.registerLanguage("roboconf", C_2());
  M1.registerLanguage("routeros", w_2());
  M1.registerLanguage("rsl", A_2());
  M1.registerLanguage("ruleslanguage", X_2());
  M1.registerLanguage("rust", __2());
  M1.registerLanguage("sas", H_2());
  M1.registerLanguage("scala", g_2());
  M1.registerLanguage("scheme", K_2());
  M1.registerLanguage("scilab", z_2());
  M1.registerLanguage("scss", f_2());
  M1.registerLanguage("shell", R_2());
  M1.registerLanguage("smali", v_2());
  M1.registerLanguage("smalltalk", M_2());
  M1.registerLanguage("sml", L_2());
  M1.registerLanguage("sqf", P_2());
  M1.registerLanguage("sql_more", u_2());
  M1.registerLanguage("sql", m_2());
  M1.registerLanguage("stan", b_2());
  M1.registerLanguage("stata", j_2());
  M1.registerLanguage("step21", x_2());
  M1.registerLanguage("stylus", p_2());
  M1.registerLanguage("subunit", n_2());
  M1.registerLanguage("swift", GD2());
  M1.registerLanguage("taggerscript", CD2());
  M1.registerLanguage("yaml", wD2());
  M1.registerLanguage("tap", AD2());
  M1.registerLanguage("tcl", YD2());
  M1.registerLanguage("thrift", DD2());
  M1.registerLanguage("tp", FD2());
  M1.registerLanguage("twig", JD2());
  M1.registerLanguage("typescript", qD2());
  M1.registerLanguage("vala", UD2());
  M1.registerLanguage("vbnet", MD2());
  M1.registerLanguage("vbscript", yD2());
  M1.registerLanguage("vbscript-html", $D2());
  M1.registerLanguage("verilog", TD2());
  M1.registerLanguage("vhdl", mD2());
  M1.registerLanguage("vim", bD2());
  M1.registerLanguage("x86asm", jD2());
  M1.registerLanguage("xl", xD2());
  M1.registerLanguage("xquery", pD2());
  M1.registerLanguage("zephir", nD2());
  rD2.exports = M1
})
// @from(Start 4951643, End 4953539)
la = Y((B59) => {
  var w59 = [65534, 65535, 131070, 131071, 196606, 196607, 262142, 262143, 327678, 327679, 393214, 393215, 458750, 458751, 524286, 524287, 589822, 589823, 655358, 655359, 720894, 720895, 786430, 786431, 851966, 851967, 917502, 917503, 983038, 983039, 1048574, 1048575, 1114110, 1114111];
  B59.REPLACEMENT_CHARACTER = "�";
  B59.CODE_POINTS = {
    EOF: -1,
    NULL: 0,
    TABULATION: 9,
    CARRIAGE_RETURN: 13,
    LINE_FEED: 10,
    FORM_FEED: 12,
    SPACE: 32,
    EXCLAMATION_MARK: 33,
    QUOTATION_MARK: 34,
    NUMBER_SIGN: 35,
    AMPERSAND: 38,
    APOSTROPHE: 39,
    HYPHEN_MINUS: 45,
    SOLIDUS: 47,
    DIGIT_0: 48,
    DIGIT_9: 57,
    SEMICOLON: 59,
    LESS_THAN_SIGN: 60,
    EQUALS_SIGN: 61,
    GREATER_THAN_SIGN: 62,
    QUESTION_MARK: 63,
    LATIN_CAPITAL_A: 65,
    LATIN_CAPITAL_F: 70,
    LATIN_CAPITAL_X: 88,
    LATIN_CAPITAL_Z: 90,
    RIGHT_SQUARE_BRACKET: 93,
    GRAVE_ACCENT: 96,
    LATIN_SMALL_A: 97,
    LATIN_SMALL_F: 102,
    LATIN_SMALL_X: 120,
    LATIN_SMALL_Z: 122,
    REPLACEMENT_CHARACTER: 65533
  };
  B59.CODE_POINT_SEQUENCES = {
    DASH_DASH_STRING: [45, 45],
    DOCTYPE_STRING: [68, 79, 67, 84, 89, 80, 69],
    CDATA_START_STRING: [91, 67, 68, 65, 84, 65, 91],
    SCRIPT_STRING: [115, 99, 114, 105, 112, 116],
    PUBLIC_STRING: [80, 85, 66, 76, 73, 67],
    SYSTEM_STRING: [83, 89, 83, 84, 69, 77]
  };
  B59.isSurrogate = function(I) {
    return I >= 55296 && I <= 57343
  };
  B59.isSurrogatePair = function(I) {
    return I >= 56320 && I <= 57343
  };
  B59.getSurrogatePairCodePoint = function(I, d) {
    return (I - 55296) * 1024 + 9216 + d
  };
  B59.isControlCodePoint = function(I) {
    return I !== 32 && I !== 10 && I !== 13 && I !== 9 && I !== 12 && I >= 1 && I <= 31 || I >= 127 && I <= 159
  };
  B59.isUndefinedCodePoint = function(I) {
    return I >= 64976 && I <= 65007 || w59.indexOf(I) > -1
  }
})
// @from(Start 4953545, End 4957800)
ba = Y((IJ3, aD2) => {
  aD2.exports = {
    controlCharacterInInputStream: "control-character-in-input-stream",
    noncharacterInInputStream: "noncharacter-in-input-stream",
    surrogateInInputStream: "surrogate-in-input-stream",
    nonVoidHtmlElementStartTagWithTrailingSolidus: "non-void-html-element-start-tag-with-trailing-solidus",
    endTagWithAttributes: "end-tag-with-attributes",
    endTagWithTrailingSolidus: "end-tag-with-trailing-solidus",
    unexpectedSolidusInTag: "unexpected-solidus-in-tag",
    unexpectedNullCharacter: "unexpected-null-character",
    unexpectedQuestionMarkInsteadOfTagName: "unexpected-question-mark-instead-of-tag-name",
    invalidFirstCharacterOfTagName: "invalid-first-character-of-tag-name",
    unexpectedEqualsSignBeforeAttributeName: "unexpected-equals-sign-before-attribute-name",
    missingEndTagName: "missing-end-tag-name",
    unexpectedCharacterInAttributeName: "unexpected-character-in-attribute-name",
    unknownNamedCharacterReference: "unknown-named-character-reference",
    missingSemicolonAfterCharacterReference: "missing-semicolon-after-character-reference",
    unexpectedCharacterAfterDoctypeSystemIdentifier: "unexpected-character-after-doctype-system-identifier",
    unexpectedCharacterInUnquotedAttributeValue: "unexpected-character-in-unquoted-attribute-value",
    eofBeforeTagName: "eof-before-tag-name",
    eofInTag: "eof-in-tag",
    missingAttributeValue: "missing-attribute-value",
    missingWhitespaceBetweenAttributes: "missing-whitespace-between-attributes",
    missingWhitespaceAfterDoctypePublicKeyword: "missing-whitespace-after-doctype-public-keyword",
    missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers: "missing-whitespace-between-doctype-public-and-system-identifiers",
    missingWhitespaceAfterDoctypeSystemKeyword: "missing-whitespace-after-doctype-system-keyword",
    missingQuoteBeforeDoctypePublicIdentifier: "missing-quote-before-doctype-public-identifier",
    missingQuoteBeforeDoctypeSystemIdentifier: "missing-quote-before-doctype-system-identifier",
    missingDoctypePublicIdentifier: "missing-doctype-public-identifier",
    missingDoctypeSystemIdentifier: "missing-doctype-system-identifier",
    abruptDoctypePublicIdentifier: "abrupt-doctype-public-identifier",
    abruptDoctypeSystemIdentifier: "abrupt-doctype-system-identifier",
    cdataInHtmlContent: "cdata-in-html-content",
    incorrectlyOpenedComment: "incorrectly-opened-comment",
    eofInScriptHtmlCommentLikeText: "eof-in-script-html-comment-like-text",
    eofInDoctype: "eof-in-doctype",
    nestedComment: "nested-comment",
    abruptClosingOfEmptyComment: "abrupt-closing-of-empty-comment",
    eofInComment: "eof-in-comment",
    incorrectlyClosedComment: "incorrectly-closed-comment",
    eofInCdata: "eof-in-cdata",
    absenceOfDigitsInNumericCharacterReference: "absence-of-digits-in-numeric-character-reference",
    nullCharacterReference: "null-character-reference",
    surrogateCharacterReference: "surrogate-character-reference",
    characterReferenceOutsideUnicodeRange: "character-reference-outside-unicode-range",
    controlCharacterReference: "control-character-reference",
    noncharacterCharacterReference: "noncharacter-character-reference",
    missingWhitespaceBeforeDoctypeName: "missing-whitespace-before-doctype-name",
    missingDoctypeName: "missing-doctype-name",
    invalidCharacterSequenceAfterDoctypeName: "invalid-character-sequence-after-doctype-name",
    duplicateAttribute: "duplicate-attribute",
    nonConformingDoctype: "non-conforming-doctype",
    missingDoctype: "missing-doctype",
    misplacedDoctype: "misplaced-doctype",
    endTagWithoutMatchingOpenElement: "end-tag-without-matching-open-element",
    closingOfElementWithOpenChildElements: "closing-of-element-with-open-child-elements",
    disallowedContentInNoscriptInHead: "disallowed-content-in-noscript-in-head",
    openElementsLeftAfterEof: "open-elements-left-after-eof",
    abandonedHeadElementChild: "abandoned-head-element-child",
    misplacedStartTagForHeadElement: "misplaced-start-tag-for-head-element",
    nestedNoscriptInHead: "nested-noscript-in-head",
    eofInElementThatCanContainOnlyText: "eof-in-element-that-can-contain-only-text"
  }
})
// @from(Start 4957806, End 4960217)
eD2 = Y((dJ3, oD2) => {
  var vR = la(),
    dg1 = ba(),
    xJ = vR.CODE_POINTS;
  class sD2 {
    constructor() {
      this.html = null, this.pos = -1, this.lastGapPos = -1, this.lastCharPos = -1, this.gapStack = [], this.skipNextNewLine = !1, this.lastChunkWritten = !1, this.endOfChunkHit = !1, this.bufferWaterline = 65536
    }
    _err() {}
    _addGap() {
      this.gapStack.push(this.lastGapPos), this.lastGapPos = this.pos
    }
    _processSurrogate(I) {
      if (this.pos !== this.lastCharPos) {
        let d = this.html.charCodeAt(this.pos + 1);
        if (vR.isSurrogatePair(d)) return this.pos++, this._addGap(), vR.getSurrogatePairCodePoint(I, d)
      } else if (!this.lastChunkWritten) return this.endOfChunkHit = !0, xJ.EOF;
      return this._err(dg1.surrogateInInputStream), I
    }
    dropParsedChunk() {
      if (this.pos > this.bufferWaterline) this.lastCharPos -= this.pos, this.html = this.html.substring(this.pos), this.pos = 0, this.lastGapPos = -1, this.gapStack = []
    }
    write(I, d) {
      if (this.html) this.html += I;
      else this.html = I;
      this.lastCharPos = this.html.length - 1, this.endOfChunkHit = !1, this.lastChunkWritten = d
    }
    insertHtmlAtCurrentPos(I) {
      this.html = this.html.substring(0, this.pos + 1) + I + this.html.substring(this.pos + 1, this.html.length), this.lastCharPos = this.html.length - 1, this.endOfChunkHit = !1
    }
    advance() {
      if (this.pos++, this.pos > this.lastCharPos) return this.endOfChunkHit = !this.lastChunkWritten, xJ.EOF;
      let I = this.html.charCodeAt(this.pos);
      if (this.skipNextNewLine && I === xJ.LINE_FEED) return this.skipNextNewLine = !1, this._addGap(), this.advance();
      if (I === xJ.CARRIAGE_RETURN) return this.skipNextNewLine = !0, xJ.LINE_FEED;
      if (this.skipNextNewLine = !1, vR.isSurrogate(I)) I = this._processSurrogate(I);
      if (!(I > 31 && I < 127 || I === xJ.LINE_FEED || I === xJ.CARRIAGE_RETURN || I > 159 && I < 64976)) this._checkForProblematicCharacters(I);
      return I
    }
    _checkForProblematicCharacters(I) {
      if (vR.isControlCodePoint(I)) this._err(dg1.controlCharacterInInputStream);
      else if (vR.isUndefinedCodePoint(I)) this._err(dg1.noncharacterInInputStream)
    }
    retreat() {
      if (this.pos === this.lastGapPos) this.lastGapPos = this.gapStack.pop(), this.pos--;
      this.pos--
    }
  }
  oD2.exports = sD2
})