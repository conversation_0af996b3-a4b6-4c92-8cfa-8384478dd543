name: Write
description: >-
  Writes a file to the local filesystem.


  Usage:

  - This tool will overwrite the existing file if there is one at the provided
  path.

  - If this is an existing file, you MUST use the Read tool first to read the
  file's contents. This tool will fail if you did not read the file first.

  - ALWAYS prefer editing existing files in the codebase. NEVER write new files
  unless explicitly required.

  - NEVER proactively create documentation files (*.md) or README files. Only
  create documentation files if explicitly requested by the User.

  - Only use emojis if the user explicitly requests it. Avoid writing emojis to
  files unless asked.
input_schema:
  type: object
  properties:
    file_path:
      type: string
      description: The absolute path to the file to write (must be absolute, not relative)
    content:
      type: string
      description: The content to write to the file
  required:
    - file_path
    - content
  additionalProperties: false
  $schema: http://json-schema.org/draft-07/schema#
